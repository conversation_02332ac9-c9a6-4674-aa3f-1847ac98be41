<script setup lang="ts">
const stacks = [
  {
    icon: 'simple-icons:nuxtdotjs',
    size: 'h-5 w-5',
    title: 'Nuxt',
    description: 'Nuxt.',
  },
  {
    icon: 'simple-icons:tailwindcss',
    size: 'h-5 w-5',
    title: 'Tailwindcss',
    description: 'Tailwindcss.',
  },
  {
    icon: 'lineicons:prisma',
    size: 'h-6 w-6',
    title: 'Prisma',
    description: 'Prisma.',
  },
  {
    icon: 'devicon-plain:supabase',
    size: 'h-5 w-5',
    title: 'Supabase',
    description: 'Supabase.',
  },
  {
    icon: 'simple-icons:shadcnui',
    size: 'h-4 w-4',
    title: 'Shadcn/ui',
    description: 'Shadcn.',
  },
  {
    icon: 'devicon-plain:typescript',
    size: 'h-4 w-4',
    title: 'Typescript',
    description: 'Typescript.',
  },
  {
    icon: 'simple-icons:zod',
    size: 'h-5 w-5',
    title: 'Zod',
    description: 'Zod.',
  },
]
</script>

<template>
  <Tabs default-value="Nuxt">
    <TabsList class="h-auto w-full gap-12 bg-transparent p-0 px-4">
      <TabsTrigger
        v-for="(stack, index) in stacks"
        :key="index"
        :value="stack.title"
        class="group flex flex-col items-center gap-2 p-0 data-[state=active]:shadow-none"
      >
        <div class="flex size-14 items-center justify-center rounded-xl border group-data-[state='active']:bg-gradient-to-b group-data-[state='active']:from-white/[3%]">
          <Icon
            :name="stack.icon"
            :class="stack.size"
          />
        </div>
        <span class="mt-2 flex text-sm font-normal leading-[1.6] group-hover:text-foreground group-data-[state=active]:font-semibold group-data-[state=active]:text-foreground">{{ stack.title }}</span>
      </TabsTrigger>
    </TabsList>
    <TabsContent
      v-for="(stack, index) in stacks"
      :key="index"
      :value="stack.title"
    >
      {{ stack.description }}
    </TabsContent>
  </Tabs>
</template>
