---
title: Introduction
description: Introducing the Acme, which is a fullstack starter kit that helps you build production-ready and scalable web applications faster.
---

⚡️ Welcome to the Acme for Nuxt documentation. 

Whether you're a beginner or an experienced developer, this documentation will guide you through the setup, configuration, and deployment of your SaaS application.

### Features 
With Acme, you can focus on building your product and not worry about setting up Auth, Database, Payments, and more.
If you want to try the functionality of supastarter, you can check out the [Live Demo](/).

##### Nuxt 4
Nuxt 4 introduces significant enhancements, pushing the framework closer to full integration with the latest Vue and JavaScript standards. It also features new ways to handle reactivity, file structure, and data fetching that improve developer experience.
- Enable Compatibility Mode for Nuxt 4
- Adopt the new Nuxt 4 directory structure

##### Tailwind CSS 4.0 Ready
Now that there's a v4-beta available, the new engine is a ground-up rewrite, making things faster with a lot less code. We can do a full build of the Tailwind CSS website in 105ms instead of 960ms, and over 35% smaller installed.

Our current codebase is **fully compatible with Tailwind CSS 4.0**, and we are preparing to release an update based on the 4.0-Beta version by the end of this month.

##### Authentication
- **Signup, Login, Logout:** Easy and secure user authentication.
- **Flexible Authentication Methods:** Email/Password, Social Logins (Google, GitHub, and more), Magic Links
- **Forgot Password:** Simple password recovery process.
- **Role:** Assign different roles to users within organizations.

##### Billing and Payment
- **Manage billing:** Billing settings page to manage account's subscription.
- **Subscriptions:** Support for various subscription plans, including flat, tiered, and per-seat models.
- **Multiple Billing Providers:** Out-of-the-box support for Stripe, Lemon Squeezy, and Paddle.

##### Database
- **Multiple databases:** Support PostgreSQL, MySQL, or SQLite.
- **ORM:** Uses Prisma to provide a collection of database utilities for your site

##### Mailing
- **Multiple providers:** Resend, Plunk, Postmark, Nodemailer or Custom.
- **Mail templates:** build a generic template and use it in all your mails.

##### Dashboard
- **Account Management:** Users have their own personal accounts
- **Account Settings:** Update profile and password, Avatar upload

##### Documentation
- **MDX:** Content is managed with MDX in your codebase [(Live Demo)](/docs)
- **TOC:** Automatic table of contents generation

##### Blog
- Blog and blog post pages [(Live Demo)](/blogs)
- **MDX:** Content is managed with MDX in your codebase

##### Super Admin
- **Super Admin Panel:** Allows you to manage users and accounts.
- **CRUD:** Generates one menu section for each database table. Now will be much easier to list, create, edit and delete items!

### Conclusion
This starter kit is designed to accelerate your SaaS development with a robust, modular, and extensible foundation. Start building your SaaS application today! 🚀