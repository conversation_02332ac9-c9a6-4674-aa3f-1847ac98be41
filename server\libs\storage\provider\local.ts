import { promises as fs } from 'fs'
import path from 'path'
import type { FileStorageService, FileUploadOptions } from '../types'

const runtimeConfig = useRuntimeConfig()

const baseDir = path.join(process.cwd(), 'public', 'uploads')

export const getPublicUrl = (filename: string): string => {
  return `${runtimeConfig.public.baseUrl}/uploads/${filename}`
}

export const upload = async ({ name, data }: FileUploadOptions): Promise<string> => {
  const filePath = path.join(baseDir, name)
  await fs.mkdir(path.dirname(filePath), { recursive: true })

  if (data instanceof Blob) {
    const buffer = Buffer.from(await data.arrayBuffer())
    await fs.writeFile(filePath, buffer)
  }
  else if (Buffer.isBuffer(data)) {
    await fs.writeFile(filePath, data)
  }
  else if (typeof data === 'string') {
    await fs.writeFile(filePath, data, 'utf-8')
  }
  else {
    throw new Error('Unsupported data type for upload')
  }

  return getPublicUrl(name)
}

export const deleteFile = async (filename: string): Promise<void> => {
  const filePath = path.join(baseDir, filename)
  await fs.unlink(filePath)
}
