import type { Send<PERSON><PERSON>Handler } from '../types'

export const sendEmail: SendEmailHandler = async ({ to, subject, html, from }) => {
  if (!process.env.RESEND_API_KEY) {
    throw new Error('RESEND_API_KEY is missing')
  }

  if (!to || !from || !subject || !html) {
    throw new Error('Required email fields are missing')
  }

  try {
    await $fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.RESEND_API_KEY}`,
      },
      body: {
        to,
        from,
        subject,
        html,
      },
    })

    console.log('Email sent successfully via Resend')
  }
  catch (error) {
    console.error('Failed to send email with Resend: ', error)
    throw error
  }
}
