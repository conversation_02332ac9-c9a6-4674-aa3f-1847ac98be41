<script setup lang="ts">
import { format as dateFormat } from 'date-fns'

defineProps<{
  blog: any
  idx: number
}>()
</script>

<template>
  <NuxtLink
    :href="blog._path"
    class="group inline-block h-full min-w-full rounded-xl border border-transparent p-2 transition-all sm:p-4"
  >
    <div class="flex flex-col space-y-4">
      <div class="flex flex-col space-y-2">
        <div class="relative mb-3 aspect-[2/1] w-full overflow-hidden rounded-lg border shadow-sm lg:aspect-[5/3] ">
          <Image
            class="size-full object-cover object-top transition-transform duration-200 group-hover:scale-105"
            :src="`${blog.imageUrl}?&w=512&h=512&dpr=2&q=80`"
            :alt="`${blog.title} thumbnail`"
          />
        </div>

        <div class="flex items-center space-x-1.5 text-sm text-muted-foreground ">
          <p>{{ dateFormat(blog.date, "MMM dd, yyyy") }}</p>
          <p>•</p>
          <p>{{ blog.readingTime }} minute read</p>
        </div>
        <div class="space-y-4">
          <h3 class="text-3xl">
            {{ blog.title }}
          </h3>
          <p class="text-base text-muted-foreground">
            {{ blog.description }}
          </p>
        </div>
      </div>
    </div>
  </NuxtLink>
</template>
