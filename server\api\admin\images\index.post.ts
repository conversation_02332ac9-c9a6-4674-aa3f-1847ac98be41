import { z } from 'zod'

export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)

  const { userId, url, name } = await readValidatedBody(event, body =>
    z.object({
      userId: z.string(),
      url: z.string(),
      name: z.string(),
    }).parse(body),
  )

  const image = await db.image.create({
    data: {
      userId,
      url,
      name,
    },
  })

  return image
})
