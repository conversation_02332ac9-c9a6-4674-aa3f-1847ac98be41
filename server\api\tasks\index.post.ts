import { z } from 'zod'

export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)

  const taskCount = await db.task.count({
    where: {
      userId: user.id,
    },
  })

  const userActiveSubscription = await db.subscription.findFirst({
    where: {
      userId: user.id,
      status: 'active',
    },
  })

  if (!userActiveSubscription && taskCount > 0) {
    throw createError({
      statusCode: 403,
      statusMessage: 'Free plan users can only create one task.',
    })
  }

  const { title, content } = await readValidatedBody(event, body =>
    z.object({
      title: z.string().min(1).max(50),
      content: z.string(),
    }).parse(body),
  )

  const task = await db.task.create({
    data: {
      title,
      content,
      userId: user.id,
    },
  })

  return task
})
