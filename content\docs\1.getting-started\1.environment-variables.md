---
title: Environment Variables
description: Setup environment variables for project.
---
### Overview
Environment variables are defined in the `.env` file in the root of the package.
You can find examples of all the required environment variables in the `.env.example` file.

You do not need to fill in all of these variables. Depending on the features and service providers you are using, you will need to set up the appropriate environment variables. For example, to enable GitHub OAuth, you must configure the `NUXT_OAUTH_GITHUB_CLIENT_ID` and `NUXT_OAUTH_GITHUB_CLIENT_SECRET` variables in your .env file.

Each feature's documentation will provide detailed instructions on how to configure the relevant variables for that specific functionality.

### Environment Variables List
Here's a list of the environment variables you need to set up for your project:

```bash
# Database
# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings
DATABASE_URL="file:./dev.db"
MIGRATION_DATABASE_URL="file:./dev.db"

# Email
RESEND_API_KEY=

# Auth
NUXT_SESSION_PASSWORD=
# Auth - Google
NUXT_OAUTH_GOOGLE_CLIENT_ID=
NUXT_OAUTH_GOOGLE_CLIENT_SECRET=

# Payment - Stripe
STRIPE_SECRET_KEY=
STRIPE_PUBLIC_KEY=
STRIPE_WEBHOOK_SECRET=

# Payment - Lemonsqueezy
LEMONSQUEEZY_API_KEY=
LEMONSQUEEZY_STORE_ID=
LEMONSQUEEZY_WEBHOOK_SECRET=

# Payment - Paddle
VITE_PADDLE_ENV=sandbox # or `production`
VITE_PADDLE_CLIENT_TOKEN=
PADDLE_API_KEY=
PADDLE_NOTIFICATION_WEBHOOK_SECRET=

# Storage - s3
S3_ACCESS_KEY_ID=
S3_SECRET_ACCESS_KEY=
S3_BUCKET_NAME=
S3_ENDPOINT=
S3_PUBLIC_ACCESS_URL=

# Analytics
VITE_GOOGLE_ANALYTICS_ID=

```