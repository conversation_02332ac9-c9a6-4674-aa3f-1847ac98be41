import type { Send<PERSON><PERSON>Handler } from '../types'

export const sendEmail: SendEmailHandler = async ({ to, from, subject, html }) => {
  if (!process.env.POSTMARK_SERVER_TOKEN) {
    throw new Error('POSTMARK_SERVER_TOKEN is missing')
  }

  if (!to || !from || !html) {
    throw new Error('Required email fields are missing')
  }

  try {
    await $fetch('https://api.postmarkapp.com/email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Postmark-Server-Token': process.env.POSTMARK_SERVER_TOKEN as string,
      },
      body: JSON.stringify({
        To: to,
        From: from,
        Subject: subject,
        HtmlBody: html,
        MessageStream: 'outbound',
      }),
    })
    console.log('Email sent successfully via Postmark')
  }
  catch (error) {
    console.error('Failed to send email with Postmark: ', error)
    throw error
  }
}
