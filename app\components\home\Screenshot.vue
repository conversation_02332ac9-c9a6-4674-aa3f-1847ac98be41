<script setup lang="ts">
defineProps({
  src: {
    type: String,
    required: true,
  },
})
</script>

<template>
  <div class="relative overflow-hidden rounded-3xl border">
    <div
      aria-hidden="true"
      class="absolute right-0 top-0 z-10 h-px w-[300px] "
      style="background: linear-gradient(90deg, rgba(56, 189, 248, 0) 0%, rgba(56, 189, 248, 0) 0%, rgba(232, 232, 232, 0.2) 33.02%, rgba(143, 143, 143, 0.67) 64.41%, rgba(236, 72, 153, 0) 98.93%);"
    />
    <div
      aria-hidden="true"
      class="absolute bottom-0 left-0 z-10 h-px w-[300px]"
      style="background: linear-gradient(90deg, rgba(56, 189, 248, 0) 0%, rgba(56, 189, 248, 0) 0%, rgba(232, 232, 232, 0.2) 33.02%, rgba(143, 143, 143, 0.67) 64.41%, rgba(236, 72, 153, 0) 98.93%);"
    />

    <header class="flex h-12 items-center rounded-t-3xl border-b bg-gray-200 px-4 dark:bg-background">
      <div class="flex w-full justify-between">
        <div class="flex items-center gap-2">
          <div
            aria-hidden="true"
            class="size-2.5 rounded-full bg-red-500"
          />
          <div
            aria-hidden="true"
            class="size-2.5 rounded-full bg-yellow-500"
          />
          <div
            aria-hidden="true"
            class="size-2.5 rounded-full bg-green-500"
          />
        </div>
      </div>
    </header>
    <div class="flex w-full flex-col">
      <Image :src="src" />
    </div>
  </div>
</template>
