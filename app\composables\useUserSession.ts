import { authClient } from '@/utils/auth-client'

// 基于 better-auth 的方法，兼容了 nuxt-auth-utils 的 useUserSession，避免修改太多代码
export function useUserSession() {
  const session = authClient.useSession()

  // 计算登录状态
  const loggedIn = computed(() => !!session.value?.data?.user)

  // 用户信息
  const user = computed(() => session.value?.data?.user || null)

  // 清除会话（登出）
  const clear = async () => {
    await authClient.signOut()
  }

  return {
    // 会话状态
    loggedIn: readonly(loggedIn),
    user: readonly(user),

    // 方法 - 直接使用 session 对象的方法
    refetch: (session as any).refetch,
    clear,

    // 原始会话对象
    session
  }
}