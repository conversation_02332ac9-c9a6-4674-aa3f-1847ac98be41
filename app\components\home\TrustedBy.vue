<script setup lang="ts">
const brands = [
  {
    icon: 'devicon-plain:netlify-wordmark',
    width: 105,
  },
  {
    icon: 'devicon-plain:slack-wordmark',
    width: 105,
  },
  {
    icon: 'devicon-plain:tailwindcss-wordmark',
    width: 135,
  },
  {
    icon: 'devicon-plain:behance-wordmark',
    width: 95,
  },
  {
    icon: 'devicon-plain:stackblitz-wordmark',
    width: 120,
  },
  {
    icon: 'devicon-plain:supabase-wordmark',
    width: 125,
  },
  {
    icon: 'devicon-plain:dataspell-wordmark',
    width: 120,
  },
  {
    icon: 'devicon-plain:clarity-wordmark',
    width: 100,
  },
  {
    icon: 'devicon-plain:fiber',
    width: 75,
  },
  {
    icon: 'devicon-plain:couchbase-wordmark',
    width: 125,
  },
  {
    icon: 'devicon-plain:nuxtjs-wordmark',
    width: 105,
  },
  {
    icon: 'devicon-plain:storybook-wordmark',
    width: 110,
  },
]
</script>

<template>
  <div class="flex flex-col items-center">
    <p class="mb-10 max-w-lg text-center text-base text-muted-foreground md:text-lg md:leading-7">
      Trusted by the best engineering teams
    </p>
    <div class="grid grid-cols-2 items-center gap-x-4 overflow-hidden sm:grid-cols-3 lg:grid-cols-6">
      <div
        v-for="(brand, index) in brands"
        :key="index"
        class="flex h-20 items-center justify-center"
      >
        <Icon
          class="overflow-hidden"
          :width="brand.width"
          :name="brand.icon"
        />
      </div>
    </div>
  </div>
</template>
