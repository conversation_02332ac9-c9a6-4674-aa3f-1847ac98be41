import { PrismaClient } from '@prisma/client'

declare let global: { prisma: PrismaClient }

// eslint-disable-next-line import/no-mutable-exports
let prisma: PrismaClient

// db 只在 server 端使用，因此 PrismaClient 不需要放到 app 中去
// 开发环境：使用  global 对象来缓存  PrismaClient 实例，这是正确的做法，因为：
// Next.js 在开发模式下会频繁重新加载模块（热重载），使用  global 可以避免创建多个数据库连接
if (process.env.NODE_ENV === 'production') {
  prisma = new PrismaClient()
} else {
  if (!global.prisma) {
    global.prisma = new PrismaClient()
  }

  prisma = global.prisma
}

export { prisma as db }
