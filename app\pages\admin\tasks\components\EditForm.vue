<script setup lang="ts">
import { z } from 'zod'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'

const props = defineProps<{
  open: boolean
  task: any | null
}>()

const emit = defineEmits<{
  openChange: [open: boolean]
  saved: [task: object | null]
}>()

const taskSchema = toTypedSchema(
  z.object({
    title: z.string().min(2),
    content: z.string(),
  }),
)
const form = useForm({
  validationSchema: taskSchema,
})

const loading = ref(false)
const onSubmit = form.handleSubmit(async (values) => {
  try {
    loading.value = true
    const updatedTask = await $fetch(`/api/admin/tasks/${props.task.id}`, { method: 'PATCH', body: values })
    emit('saved', updatedTask)
    emit('openChange', false)
    toast('Task saved')
  }
  catch (error) {
    console.warn('Error: ', error)
    loading.value = false
    toast.error(error.data ? error.data.statusMessage : 'An unknown error occurred')
  }
})
</script>

<template>
  <Sheet
    :open="open"
    @update:open="(value: boolean) => emit('openChange', value)"
  >
    <SheetContent class="sm:max-w-[425px]">
      <SheetHeader>
        <SheetTitle>Edit Task</SheetTitle>
        <SheetDescription>
          Make changes to task here. Click save when you're done.
        </SheetDescription>
      </SheetHeader>

      <form
        id="editForm"
        class="mt-8 flex flex-col items-stretch gap-4"
        :validation-schema="taskSchema"
        @submit="onSubmit"
      >
        <FormField
          v-slot="{ componentField }"
          name="title"
          :value="task.title"
        >
          <FormItem>
            <FormLabel
              class="text-muted-foreground"
              for="title"
            >
              Title
            </FormLabel>
            <FormControl>
              <Input
                type="text"
                placeholder="title"
                v-bind="componentField"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
        <FormField
          v-slot="{ componentField }"
          name="content"
          :value="task.content"
        >
          <FormItem>
            <FormLabel
              class="text-muted-foreground"
              for="content"
            >
              Content
            </FormLabel>
            <FormControl>
              <Textarea v-bind="componentField" />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
      </form>

      <SheetFooter class="mt-8">
        <Button
          type="submit"
          form="editForm"
        >
          Save changes
        </Button>
      </SheetFooter>
    </SheetContent>
  </Sheet>
</template>
