import { generateEncryptK<PERSON>, resetEncrypt<PERSON>ey, restoreDataKey, encryptGCM, decryptGCM, generateIv } from '@/utils/crypto'
interface EncryptKeyResponse {
  encryptSalt: string;
  encryptKey: string;
  encryptIv: string;
  encryptAlgo: string;
}

// CryptoKey 对象是 Web Crypto API 的一部分，它是不可序列化的，因此无法直接存储到 chrome.storage.local 或其他基于字符串的存储系统（如 localStorage、IndexedDB 的普通键值存储）
const userDataKey = ref<CryptoKey | null>(null);
// const userEncryptSalt = ref<string>();
// const userEncryptKey = ref<string>();
// const userEncryptIv = ref<string>();
// const userEncryptAlgo = ref<string>();
const isLocked = computed(() => userDataKey.value === null);

export function useCrypto() {
  const { loggedIn, user } = useUserSession()
  // 使用类型断言访问自定义字段
  const userEncryptSalt = ref<string>(user?.encryptSalt);
  const userEncryptKey = ref<string>(user?.encryptKey);
  const userEncryptIv = ref<string>(user?.encryptIv);
  const userEncryptAlgo = ref<string>(user?.encryptAlgo);

  const getUserEncryptKey = async () => {
    if (!loggedIn.value) {
      return;
    }
    try {
      const res = await $fetch<EncryptKeyResponse>(`/api/account/encrypt-key`);
      userEncryptSalt.value = res.encryptSalt;
      userEncryptKey.value = res.encryptKey;
      userEncryptIv.value = res.encryptIv;
      userEncryptAlgo.value = res.encryptAlgo;
    } catch (error) {
      toast.error("Failed to fetch encrypt key data");
    }
  };

  const setUserEncryptKey = async (password: string) => {
    if (userEncryptSalt.value || userEncryptKey.value || userEncryptIv.value) {
      toast.error("Don't set encrypt key twice.");
      return;
    }

    const { dataKey, encryptSalt, encryptKey, encryptIv, encryptAlgo } = await generateEncryptKey(password);

    try {
      const res = await $fetch<EncryptKeyResponse>(`/api/account/encrypt-key`, { 
        method: 'POST', 
        body: { 
          encryptSalt, 
          encryptKey, 
          encryptIv, 
          encryptAlgo 
        } 
      })
      userDataKey.value = dataKey;
      userEncryptSalt.value = res.encryptSalt;
      userEncryptKey.value = res.encryptKey;
      userEncryptIv.value = res.encryptIv;
      userEncryptAlgo.value = res.encryptAlgo;
      toast.success("Encrypt key generated successfully");
    } catch (error) {
      toast.error("Failed to generate encrypt key");
    }
  };

  // 重置主密码
  const resetUserEncryptKey = async (newPassword: string) => {
    if (!userDataKey.value || !userEncryptSalt.value) {
      toast.error("Missing required data for password change.");
      return;
    }

    try {
      const { encryptKey, encryptIv } = await resetEncryptKey(
        newPassword,
        userDataKey.value,
        userEncryptSalt.value
      );

      // 更新本地状态
      userEncryptKey.value = encryptKey;
      userEncryptIv.value = encryptIv;

      // 调用 API 更新数据库，仅更新 encryptKey 和 encryptIv, 其他字段保持不变, 否则将无法解密之前用旧主密钥加密的 dataKey
      await $fetch(`/api/account/encrypt-key`, {
        method: "POST",
        body: { 
          encryptSalt: userEncryptSalt.value,
          encryptKey, 
          encryptIv, 
          encryptAlgo: userEncryptAlgo.value 
        },
      });

      toast.success("Password changed successfully!");
    } catch (error) {
      toast.error("Failed to change password: " + error.message);
    }
  };

  const unlock = async (password: string) => {  
    if (!userEncryptSalt.value || !userEncryptKey.value || !userEncryptIv.value) {
      throw new Error("Missing required encryption parameters");
    }
    try {
      userDataKey.value = await restoreDataKey(
        password,
        userEncryptSalt.value,
        userEncryptKey.value,
        userEncryptIv.value
      );
    } catch (error) {
      toast.error("Failed to unlock");
    }
  };

  const lock = async () => {
    userDataKey.value = null;
  }

  // encryption/decryption
  // 添加 encrypt 和 decrypt 方法
  const encryptString = async (data: string): Promise<any | null> => {
    if (!userDataKey.value) {
      toast.error("Please unlock the encryption system first");
      return null;
    }
    try {
      const iv = arrayBufferToBase64(generateIv().buffer);
      const { ciphertext, iv: encryptedDataKeyIV } = await encryptGCM(
        data,
        iv,
        userDataKey.value
      );
      return { ciphertext, iv };
    } catch (error) {
      toast.error("Failed to encrypt");
      return null;
    }
  };

  const decryptString = async (encryptedData: string, iv: string): Promise<string | null> => {
    if (!userDataKey.value) {
      toast.error("Please unlock the encryption system first");
      return null;
    }
    try {
      const decrypted = await decryptGCM(encryptedData, iv, userDataKey.value);
      return decrypted;
    } catch (error) {
      toast.error("Failed to decrypt");
      return null;
    }
  };

  // 加密书签的 title 和 url，保持相同的字段名
  const encryptBookmark = async (bookmark: any): Promise<any | null> => {
    if (!userDataKey.value) {
      toast.error("Please unlock the encryption system first");
      return null;
    }

    try {
      // 创建一个新对象，避免修改原始对象
      const result = { ...bookmark };
      
      // 直接使用 encryptGCM 加密 title
      const { ciphertext: encryptedTitle, iv } = await encryptGCM(
        bookmark.title,
        arrayBufferToBase64(generateIv().buffer),
        userDataKey.value
      );
      
      result.title = encryptedTitle;
      result.iv = iv; // 保存 IV 以便解密
      
      // 如果有 url，使用相同的 IV 加密
      if (bookmark.url) {
        const { ciphertext: encryptedUrl } = await encryptGCM(
          bookmark.url,
          iv,
          userDataKey.value
        );
        result.url = encryptedUrl;
      }

      return result;
    } catch (error) {
      toast.error("Failed to encrypt bookmark");
      return null;
    }
  };

  // 解密书签的 title 和 url，保持相同的字段名
  const decryptBookmark = async (bookmark: any): Promise<any | null> => {
    if (!userDataKey.value) {
      toast.error("Please unlock the encryption system first");
      return null;
    }

    try {
      // 创建一个新对象，避免修改原始对象
      const result = { ...bookmark };
      
      // 解密 title
      result.title = await decryptGCM(
        bookmark.title,
        bookmark.iv,
        userDataKey.value
      );
      
      // 如果有 url，也解密
      if (bookmark.url) {
        result.url = await decryptGCM(
          bookmark.url,
          bookmark.iv,
          userDataKey.value
        );
      }

      return result;
    } catch (error) {
      toast.error("Failed to decrypt bookmark");
      return null;
    }
  };

  return {
    isLocked,
    setUserEncryptKey,
    getUserEncryptKey,
    unlock,
    lock,
    encryptString,
    decryptString,
    resetUserEncryptKey,
    encryptBookmark,
    decryptBookmark
  };
}
