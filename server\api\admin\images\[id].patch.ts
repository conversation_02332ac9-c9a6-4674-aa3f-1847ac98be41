import { z } from 'zod'

export default defineEventHandler(async (event) => {
  const id = getRouterParam(event, 'id')

  const schema = z.object({
    userId: z.string(),
    url: z.string(),
    name: z.string(),
  })

  const { userId, url, name } = await readValidatedBody(event, body =>
    schema.parse(body),
  )

  const image = await db.image.update({
    where: {
      id,
    },
    data: {
      userId,
      url,
      name,
    },
  })

  return image
})
