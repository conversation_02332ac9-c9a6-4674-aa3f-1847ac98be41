export default defineEventHandler(async (event) => {
  try {
    const { user } = await requireUserSession(event)

    const query = getQuery(event)
    const orderBy = JSON.parse(String(query.orderBy || '{"createdAt":"desc"}'))

    const where: any = {
      userId: user.id,
      deleted: false,
    }

    const shareds = await db.shared.findMany({
      where,
      orderBy
    })

    return shareds 
  } catch (error) {
    // 返回自定义错误响应
    throw createError({
      statusCode: 500,
      statusMessage: 'Error fetching bookmarks'
    })
  }
})
