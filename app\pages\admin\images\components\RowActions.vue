<script setup lang="ts">
import type { Row } from '@tanstack/vue-table'
import { computed } from 'vue'

interface DataTableRowActionsProps {
  row: Row<any>
}
const props = defineProps<DataTableRowActionsProps>()

const image = computed(() => props.row.original)

const emit = defineEmits<{
  remove: [image: object | null]
}>()

const deleteImage = async () => {
  try {
    await $fetch(`/api/admin/images/${image.value.id}`, {
      method: 'DELETE',
    })
    emit('remove', image)
    toast.success('Image deleted')
  }
  catch (error) {
    toast.error('Failed to delete image')
  }
}
</script>

<template>
  <div class="flex justify-end">
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <Button
          variant="ghost"
          class="flex size-8 p-0 data-[state=open]:bg-muted"
        >
          <Icon name="radix-icons:dots-horizontal" class="size-4" />
          <span class="sr-only">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        class="w-[160px]"
      >
        <DropdownMenuItem @click="deleteImage">
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
</template>
