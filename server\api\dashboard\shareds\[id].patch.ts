import { z } from 'zod'

export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)

  const sharedId = getRouterParam(event, 'id')
  const schema = z.object({
    description: z.string().optional(),
    isPublic: z.boolean().optional(),
  })

  const { description, isPublic } = await readValidatedBody(event, body =>
    schema.parse(body),
  )

  const shared = await db.shared.update({
    where: {
      id: sharedId,
      userId: user.id,
    },
    data: {
      description,
      isPublic,
      updatedAt: new Date(),
    },
  })

  return shared
})
