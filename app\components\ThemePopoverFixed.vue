<script setup lang="ts">
</script>

<template>
  <Popover>
    <PopoverTrigger as-child>
      <div class="fixed bottom-10 left-1/2 z-50 -translate-x-1/2 transition">
        <div class="flex items-center gap-x-1 rounded-full bg-background p-1 text-sm shadow-lg shadow-black/20 ring-1 ring-black/10 dark:ring-white/10">
          <Button
            variant="secondary"
            class="rounded-full"
          >
            <Icon
              name="lucide:paintbrush"
              class="mr-2"
            /> Theme Customize
          </Button>
        </div>
      </div>
    </PopoverTrigger>
    <PopoverContent
      :side-offset="8"
      align="center"
      class="w-96"
    >
      <ThemeCustomizer />
    </PopoverContent>
  </Popover>
</template>
