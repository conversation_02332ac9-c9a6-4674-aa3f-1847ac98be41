import { createClient } from '@supabase/supabase-js'

let supabaseClient: ReturnType<typeof createClient> | null = null
const bucket = process.env.SUPABASE_BUCKET_NAME ?? ''
const supabaseUrl = process.env.NUXT_PUBLIC_SUPABASE_URL ?? ''

export const getPublicUrl = (filename: string): string => {
  return `${supabaseUrl}/${filename}`
}

const getSupabaseClient = () => {
  if (supabaseClient) {
    return supabaseClient
  }

  if (!supabaseUrl) {
    console.error('Missing env variable NUXT_PUBLIC_SUPABASE_URL')
  }

  const supabaseServiceRoleKey = process.env
    .SUPABASE_SERVICE_ROLE_KEY as string
  if (!supabaseServiceRoleKey) {
    console.error('Missing env variable SUPABASE_SERVICE_ROLE_KEY')
  }

  supabaseClient = createClient(supabaseUrl, supabaseServiceRoleKey)

  return supabaseClient
}

export const getClientUploadUrl = async (name: string): Promise<string> => {
  const supabaseClient = getSupabaseClient()
  const { data, error } = await supabaseClient.storage
    .from(bucket)
    .createSignedUploadUrl(name)

  if (error) {
    console.error(error)
    throw new Error('Could not get signed url')
  }

  return data.signedUrl
}
