type SelectParams = {
  selectedIds: any[];
  selectedId: any;
  id: any;
};

type SelectAllParams = {
  selectedIds: any[];
  selectedId: any;
  ids: any[];
  id: any;
};

export function select({
  selectedIds,
  selectedId,
  id,
}: SelectParams): [any, any[]] {
  if (!selectedIds.includes(id)) return [id, selectedIds.concat(id)];

  if (selectedIds.length === 1)
    return selectedIds.includes(id) ? [id, []] : [selectedId, selectedIds];

  const pivotIndex = selectedIds.findIndex((x) => x === id);
  const nextSelectedIndex =
    selectedIds[pivotIndex + 1] ?? selectedIds[pivotIndex - 1] ?? id;
  const nextSelectedIndices = selectedIds.filter((x) => x !== id);

  return [nextSelectedIndex, nextSelectedIndices];
}

export function selectAll({
  selectedIds,
  selectedId,
  ids,
  id,
}: SelectAllParams): any[] {
  const reverseIndexLookup = (x: any) => ids.indexOf(x);
  const _index = reverseIndexLookup(id);
  const _selectedIndex = reverseIndexLookup(selectedId);
  const _selectedIndices = selectedIds.map(reverseIndexLookup).filter((index): index is number => index !== -1);

  if (selectedIds.length <= 1)
    return _selectAll(ids, _selectedIndex, _index);

  const nonConsecutiveIndex = _getNonConsecutiveIndex(_selectedIndices);

  if (nonConsecutiveIndex === -1)
    return _selectAll(ids, _selectedIndex, _index);

  if (reverseIndexLookup(selectedId) > reverseIndexLookup(id)) {
    // current selection extends backward to meet target
    return ids.slice(_index, _selectedIndex + 1);
  }

  // select remaining indices
  return selectedIds
    .slice(0, nonConsecutiveIndex)
    .concat(ids.slice(_selectedIndex, _index + 1));
}

function _selectAll(indices: any[], selectedIndex: number, index: number) {
  if (selectedIndex > index) {
    // current selection extends backward to meet target
    return indices.slice(index, selectedIndex + 1);
  }

  // current selection extends forward to meet target
  return indices.slice(selectedIndex, index + 1);
}

function _getNonConsecutiveIndex(selectedIndices: number[]): number {
  let nonConsecutiveIndex = -1;

  for (let i = 1, len = selectedIndices.length; i < len; i++) {
    const prev = selectedIndices[i - 1];
    const current = selectedIndices[i];
    
    if (prev !== undefined && current !== undefined) {
      if (prev > current || Math.abs(prev - current) > 1) {
        nonConsecutiveIndex = i;
      }
    }
  }

  return nonConsecutiveIndex;
}