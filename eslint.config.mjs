import pluginTailwindCSS from 'eslint-plugin-tailwindcss'
import withNuxt from './.nuxt/eslint.config.mjs'

export default withNuxt(
  // your custom flat configs go here, for example:
  {
    plugins: { tailwindcss: pluginTailwindCSS },
    rules: {
      ...pluginTailwindCSS.configs.recommended.rules,
      'tailwindcss/no-custom-classname': 'off', // allow custom classname
      'no-console': 'off', // allow console.log in TypeScript files
      'vue/no-multiple-template-root': 'off',
      'vue/multi-word-component-names': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
      'no-unused-vars': 'off',
      '@typescript-eslint/no-explicit-any': ['off'],
    },
  },
)
