// 共享的连接池（模块作用域）
const sseConnections = new Map<string, ReturnType<typeof createEventStream>>()

export function useSSE() {
  function sendToUser(userId: string, message: string) {
    const connection = sseConnections.get(userId)
    if (connection) {
      connection.push(message)
    } else {
      console.warn('-------- sse not found! ----------', userId)
    }
  }

  return {
    sseConnections,
    sendToUser
  }
}