<script setup lang="ts">
import { z } from 'zod'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'

const props = defineProps<{
  open: boolean
}>()

const emit = defineEmits<{
  openChange: [open: boolean]
  saved: [task: object | null]
}>()

const taskSchema = toTypedSchema(
  z.object({
    title: z.string().min(2),
    content: z.string(),
  }),
)
const form = useForm({
  validationSchema: taskSchema,
})

const loading = ref(false)
const onSubmit = form.handleSubmit(async (values) => {
  try {
    loading.value = true
    const updatedTask = await $fetch(`/api/admin/tasks`, { method: 'POST', body: values })
    emit('saved', updatedTask)
    emit('openChange', false)
    toast('Task created')
  }
  catch (error) {
    console.warn('Error: ', error)
    toast.error(error.data ? error.data.statusMessage : 'An unknown error occurred')
  }
  finally {
    loading.value = false
  }
})
</script>

<template>
  <Sheet
    :open="open"
    direction="right"
    @update:open="(value: boolean) => emit('openChange', value)"
  >
    <SheetContent>
      <SheetHeader>
        <SheetTitle>New Task</SheetTitle>
        <SheetDescription>
          Make new task here. Click save when you're done.
        </SheetDescription>
      </SheetHeader>

      <form
        id="newForm"
        class="mt-8 flex flex-col items-stretch gap-4"
        :validation-schema="taskSchema"
        @submit="onSubmit"
      >
        <FormField
          v-slot="{ componentField }"
          name="title"
        >
          <FormItem>
            <FormLabel
              class="text-muted-foreground"
              for="title"
            >
              Title
            </FormLabel>
            <FormControl>
              <Input
                type="text"
                placeholder="title"
                v-bind="componentField"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
        <FormField
          v-slot="{ componentField }"
          name="content"
        >
          <FormItem>
            <FormLabel
              class="text-muted-foreground"
              for="content"
            >
              Content
            </FormLabel>
            <FormControl>
              <Textarea v-bind="componentField" />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
      </form>

      <SheetFooter class="mt-8">
        <Button
          type="submit"
          form="newForm"
          :disabled="loading"
        >
          <Icon
            v-if="loading"
            name="svg-spinners:3-dots-fade"
            class="size-5"
          />
          <span v-else>Create</span>
        </Button>
      </SheetFooter>
    </SheetContent>
  </Sheet>
</template>
