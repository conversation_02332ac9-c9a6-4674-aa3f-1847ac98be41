export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)

  const query = getQuery(event)
  const updatedAt = query.updatedAt as string
  const limit = parseInt(query.limit as string)

  const shareds = await db.shared.findMany({
    where: {
      userId: user.id,
      updatedAt: { gt: new Date(updatedAt) }
    },
    take: limit,
    orderBy: {
      updatedAt: 'asc'
    }
  })

  // 将 shareds 中的 updatedAt 转换为毫秒数，似乎无必要，默认保存就是毫秒数
  const sharedsWithMillis = shareds.map(shared => ({
    ...shared,
    // updatedAt: shared.updatedAt.getTime(),
    // createdAt: shared.createdAt.getTime()
  }));

  const newCheckpoint = sharedsWithMillis.length === 0 ? { updatedAt } : {
    updatedAt: sharedsWithMillis[sharedsWithMillis.length - 1].updatedAt
  };

  return { documents: sharedsWithMillis, checkpoint: newCheckpoint }
})
