<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuContent } from '@/components/ui/dropdown-menu'

const props = defineProps<{
  sortBy: {
    field: 'default' | 'title' | 'dateAdded' | 'url' | 'id' | 'dateLastUsed';
    direction: 'asc' | 'desc';
  };
}>();

const emit = defineEmits(['sort']);

const sortOptions = [
  { value: 'default', label: 'Default' },
  { value: 'title', label: 'Title' },
  { value: 'dateAdded', label: 'Date Added' },
  { value: 'url', label: 'URL' },
  { value: 'id', label: 'ID' },
  { value: 'dateLastUsed', label: 'Date Last Used' },
];

function getSortDirectionIcon(field: string) {
  return props.sortBy.direction === 'asc' 
    ? 'lucide:arrow-up-down' 
    : 'lucide:arrow-down-up';
}
</script>

<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <Button variant="outline" size="sm" class="h-8">
        <Icon 
          :name="getSortDirectionIcon(sortBy.field)" 
          class="h-4 w-4"
        />
        {{ sortBy.field === 'default' ? 'Sort' : sortOptions.find(option => option.value === sortBy.field)?.label }}
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="end">
      <DropdownMenuItem 
        v-for="option in sortOptions" 
        :key="option.value"
        @click="emit('sort', option.value)"
      >
          <Icon 
            v-if="sortBy.field === option.value"
            :name="getSortDirectionIcon(option.value)" 
            class="h-4 w-4 text-muted-foreground"
          />
          <span v-else class="flex items-center h-4 w-4"></span>
        {{ option.label }}
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>
