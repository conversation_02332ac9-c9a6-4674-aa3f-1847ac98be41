export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)

  const query = getQuery(event)
  const page = Number(query.page || 1)
  const pageSize = Number(query.pageSize || 10)
  const search = String(query.search || '')
  const filter = String(query.filter || 'all')
  const orderBy = JSON.parse(String(query.orderBy || '{"createdAt":"desc"}'))

  const searchConditions = query.search
    ? {
        OR: [
          {
            title: {
              contains: String(query.search),
            },
          },
        // Search for more fields
        ],
      }
    : null

  const where = {
    userId: user.id,
    ...searchConditions,
  }

  const userTasksCount = await db.task.count({
    where: {
      userId: user.id,
    },
  })

  const [tasks, total] = await db.$transaction([
    db.task.findMany({
      where,
      skip: (page - 1) * pageSize || 0,
      take: pageSize || 20,
      orderBy: {
        createdAt: 'desc',
      },
    }),
    db.task.count({
      where,
    }),
  ])

  return { tasks, total, userTasksCount }
})
