export const useAnalytics = () => {
  const init = () => {
    if (!import.meta.client) {
      return
    }

    useHead({
      script: [
        // Add your script here
        /*
        {
          key: 'analytics-custom',
          src: 'https://example.com/script.js',
          'data-tracking-id': '',
          async: true,
        },
        */
      ],
    })
  }

  const trackEvent = (event: string, data?: Record<string, unknown>) => {
    // call your analytics service to track a custom event here

    console.info('tracking event', event, data)
  }

  return {
    init,
    trackEvent,
  }
}
