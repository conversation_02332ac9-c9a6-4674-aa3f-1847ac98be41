
:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.269 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.371 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.439 0 0);
}

.theme-supa {
  --background: hsl(0 0% 100%);
  --foreground: hsl(240 10% 3.9%);

  --muted: hsl(240 4.8% 95.9%);
  --muted-foreground: hsl(240 3.8% 46.1%);

  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(240 10% 3.9%);

  --card: hsl(0 0% 100%);
  --card-foreground: hsl(240 10% 3.9%);

  --border: hsl(240 5.9% 90%);
  --input: hsl(240 5.9% 90%);

  --primary: hsl(155.3deg 78.4% 40%);
  --primary-foreground: hsl(0 0% 98%);

  --secondary: hsl(240 4.8% 95.9%);
  --secondary-foreground: hsl(240 5.9% 10%);

  --accent: hsl(240 4.8% 95.9%);
  --accent-foreground: hsl(240 5.9% 10%);

  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(0 0% 98%);

  --ring: hsl(155.3deg 78.4% 40%);
}

.theme-supa.dark {
  --background: hsl(240 10% 3.9%);
  --foreground: hsl(0 0% 98%);

  --muted: hsl(240 3.7% 15.9%);
  --muted-foreground: hsl(240 5% 64.9%);

  --popover: hsl(240 10% 3.9%);
  --popover-foreground: hsl(0 0% 98%);

  --card: hsl(240 10% 3.9%);
  --card-foreground: hsl(0 0% 98%);

  --border: hsl(240 3.7% 15.9%);
  --input: hsl(240 3.7% 15.9%);

  --primary: hsl(154.9 100% 19.2%);
  --primary-foreground: hsl(355.7 100% 97.3%);

  --secondary: hsl(240 3.7% 15.9%);
  --secondary-foreground: hsl(0 0% 98%);

  --accent: hsl(240 3.7% 15.9%);
  --accent-foreground: hsl(0 0% 98%);

  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(0 0% 98%);

  --ring: hsl(154.9 100% 19.2%);
}

.theme-zinc {
  --background: hsl(0 0% 100%);
  --foreground: hsl(240 10% 3.9%);

  --muted: hsl(240 4.8% 95.9%);
  --muted-foreground: hsl(240 3.8% 46.1%);

  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(240 10% 3.9%);

  --card: hsl(0 0% 100%);
  --card-foreground: hsl(240 10% 3.9%);

  --border: hsl(240 5.9% 90%);
  --input: hsl(240 5.9% 90%);

  --primary: hsl(240 5.9% 10%);
  --primary-foreground: hsl(0 0% 98%);

  --secondary: hsl(240 4.8% 95.9%);
  --secondary-foreground: hsl(240 5.9% 10%);

  --accent: hsl(240 4.8% 95.9%);
  --accent-foreground: hsl(240 5.9% 10%);

  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(0 0% 98%);

  --ring: hsl(240 5.9% 10%);
}

.theme-zinc.dark {
  --background: hsl(240 10% 3.9%);
  --foreground: hsl(0 0% 98%);

  --muted: hsl(240 3.7% 15.9%);
  --muted-foreground: hsl(240 5% 64.9%);

  --popover: hsl(240 10% 3.9%);
  --popover-foreground: hsl(0 0% 98%);

  --card: hsl(240 10% 3.9%);
  --card-foreground: hsl(0 0% 98%);

  --border: hsl(240 3.7% 15.9%);
  --input: hsl(240 3.7% 15.9%);

  --primary: hsl(0 0% 98%);
  --primary-foreground: hsl(240 5.9% 10%);

  --secondary: hsl(240 3.7% 15.9%);
  --secondary-foreground: hsl(0 0% 98%);

  --accent: hsl(240 3.7% 15.9%);
  --accent-foreground: hsl(0 0% 98%);

  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(0 0% 98%);

  --ring: hsl(240 4.9% 83.9%);
}

.theme-slate {
  --background: hsl(0 0% 100%);
  --foreground: hsl(222.2 84% 4.9%);

  --muted: hsl(210 40% 96.1%);
  --muted-foreground: hsl(215.4 16.3% 46.9%);

  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(222.2 84% 4.9%);

  --card: hsl(0 0% 100%);
  --card-foreground: hsl(222.2 84% 4.9%);

  --border: hsl(214.3 31.8% 91.4%);
  --input: hsl(214.3 31.8% 91.4%);

  --primary: hsl(222.2 47.4% 11.2%);
  --primary-foreground: hsl(210 40% 98%);

  --secondary: hsl(210 40% 96.1%);
  --secondary-foreground: hsl(222.2 47.4% 11.2%);

  --accent: hsl(210 40% 96.1%);
  --accent-foreground: hsl(222.2 47.4% 11.2%);

  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(210 40% 98%);

  --ring: hsl(222.2 84% 4.9%);
}

.theme-slate.dark {
  --background: hsl(222.2 84% 4.9%);
  --foreground: hsl(210 40% 98%);

  --muted: hsl(217.2 32.6% 17.5%);
  --muted-foreground: hsl(215 20.2% 65.1%);

  --popover: hsl(222.2 84% 4.9%);
  --popover-foreground: hsl(210 40% 98%);

  --card: hsl(222.2 84% 4.9%);
  --card-foreground: hsl(210 40% 98%);

  --border: hsl(217.2 32.6% 17.5%);
  --input: hsl(217.2 32.6% 17.5%);

  --primary: hsl(210 40% 98%);
  --primary-foreground: hsl(222.2 47.4% 11.2%);

  --secondary: hsl(217.2 32.6% 17.5%);
  --secondary-foreground: hsl(210 40% 98%);

  --accent: hsl(217.2 32.6% 17.5%);
  --accent-foreground: hsl(210 40% 98%);

  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(210 40% 98%);

  --ring: hsl(212.7 26.8% 83.9);
}

.theme-stone {
  --background: hsl(0 0% 100%);
  --foreground: hsl(20 14.3% 4.1%);

  --muted: hsl(60 4.8% 95.9%);
  --muted-foreground: hsl(25 5.3% 44.7%);

  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(20 14.3% 4.1%);

  --card: hsl(0 0% 100%);
  --card-foreground: hsl(20 14.3% 4.1%);

  --border: hsl(20 5.9% 90%);
  --input: hsl(20 5.9% 90%);

  --primary: hsl(24 9.8% 10%);
  --primary-foreground: hsl(60 9.1% 97.8%);

  --secondary: hsl(60 4.8% 95.9%);
  --secondary-foreground: hsl(24 9.8% 10%);

  --accent: hsl(60 4.8% 95.9%);
  --accent-foreground: hsl(24 9.8% 10%);

  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(60 9.1% 97.8%);

  --ring: hsl(20 14.3% 4.1%)  ;
}

.theme-stone.dark {
  --background: hsl(20 14.3% 4.1%);
  --foreground: hsl(60 9.1% 97.8%);

  --muted: hsl(12 6.5% 15.1%);
  --muted-foreground: hsl(24 5.4% 63.9%);

  --popover: hsl(20 14.3% 4.1%);
  --popover-foreground: hsl(60 9.1% 97.8%);

  --card: hsl(20 14.3% 4.1%);
  --card-foreground: hsl(60 9.1% 97.8%);

  --border: hsl(12 6.5% 15.1%);
  --input: hsl(12 6.5% 15.1%);

  --primary: hsl(60 9.1% 97.8%);
  --primary-foreground: hsl(24 9.8% 10%);

  --secondary: hsl(12 6.5% 15.1%);
  --secondary-foreground: hsl(60 9.1% 97.8%);

  --accent: hsl(12 6.5% 15.1%);
  --accent-foreground: hsl(60 9.1% 97.8%);

  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(60 9.1% 97.8%);

  --ring: hsl(24 5.7% 82.9%);
}

.theme-gray {
  --background: hsl(0 0% 100%);
  --foreground: hsl(224 71.4% 4.1%);

  --muted: hsl(220 14.3% 95.9%);
  --muted-foreground: hsl(220 8.9% 46.1%);

  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(224 71.4% 4.1%);

  --card: hsl(0 0% 100%);
  --card-foreground: hsl(224 71.4% 4.1%);

  --border: hsl(220 13% 91%);
  --input: hsl(220 13% 91%);

  --primary: hsl(220.9 39.3% 11%);
  --primary-foreground: hsl(210 20% 98%);

  --secondary: hsl(220 14.3% 95.9%);
  --secondary-foreground: hsl(220.9 39.3% 11%);

  --accent: hsl(220 14.3% 95.9%);
  --accent-foreground: hsl(220.9 39.3% 11%);

  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(210 20% 98%);

  --ring: hsl(224 71.4% 4.1%);
}

.theme-gray.dark {
  --background: hsl(224 71.4% 4.1%);
  --foreground: hsl(210 20% 98%);

  --muted: hsl(215 27.9% 16.9%);
  --muted-foreground: hsl(217.9 10.6% 64.9%);

  --popover: hsl(224 71.4% 4.1%);
  --popover-foreground: hsl(210 20% 98%);

  --card: hsl(224 71.4% 4.1%);
  --card-foreground: hsl(210 20% 98%);

  --border: hsl(215 27.9% 16.9%);
  --input: hsl(215 27.9% 16.9%);

  --primary: hsl(210 20% 98%);
  --primary-foreground: hsl(220.9 39.3% 11%);

  --secondary: hsl(215 27.9% 16.9%);
  --secondary-foreground: hsl(210 20% 98%);

  --accent: hsl(215 27.9% 16.9%);
  --accent-foreground: hsl(210 20% 98%);

  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(210 20% 98%);

  --ring: hsl(216 12.2% 83.9%);
}

.theme-neutral {
  --background: hsl(0 0% 100%);
  --foreground: hsl(0 0% 3.9%);

  --muted: hsl(0 0% 96.1%);
  --muted-foreground: hsl(0 0% 45.1%);

  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(0 0% 3.9%);

  --card: hsl(0 0% 100%);
  --card-foreground: hsl(0 0% 3.9%);

  --border: hsl(0 0% 89.8%);
  --input: hsl(0 0% 89.8%);

  --primary: hsl(0 0% 9%);
  --primary-foreground: hsl(0 0% 98%);

  --secondary: hsl(0 0% 96.1%);
  --secondary-foreground: hsl(0 0% 9%);

  --accent: hsl(0 0% 96.1%);
  --accent-foreground: hsl(0 0% 9%);

  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(0 0% 98%);

  --ring: hsl(0 0% 3.9%);
}

.theme-neutral.dark {
  --background: hsl(0 0% 3.9%);
  --foreground: hsl(0 0% 98%);

  --muted: hsl(0 0% 14.9%);
  --muted-foreground: hsl(0 0% 63.9%);

  --popover: hsl(0 0% 3.9%);
  --popover-foreground: hsl(0 0% 98%);

  --card: hsl(0 0% 3.9%);
  --card-foreground: hsl(0 0% 98%);

  --border: hsl(0 0% 14.9%);
  --input: hsl(0 0% 14.9%);

  --primary: hsl(0 0% 98%);
  --primary-foreground: hsl(0 0% 9%);

  --secondary: hsl(0 0% 14.9%);
  --secondary-foreground: hsl(0 0% 98%);

  --accent: hsl(0 0% 14.9%);
  --accent-foreground: hsl(0 0% 98%);

  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(0 0% 98%);

  --ring: hsl(0 0% 83.1%);
}

.theme-red {
  --background: hsl(0 0% 100%);
  --foreground: hsl(0 0% 3.9%);

  --muted: hsl(0 0% 96.1%);
  --muted-foreground: hsl(0 0% 45.1%);

  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(0 0% 3.9%);

  --card: hsl(0 0% 100%);
  --card-foreground: hsl(0 0% 3.9%);

  --border: hsl(0 0% 89.8%);
  --input: hsl(0 0% 89.8%);

  --primary: hsl(0 72.2% 50.6%);
  --primary-foreground: hsl(0 85.7% 97.3%);

  --secondary: hsl(0 0% 96.1%);
  --secondary-foreground: hsl(0 0% 9%);

  --accent: hsl(0 0% 96.1%);
  --accent-foreground: hsl(0 0% 9%);

  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(0 0% 98%);

  --ring: hsl(0 72.2% 50.6%);
}

.theme-red.dark {
  --background: hsl(0 0% 3.9%);
  --foreground: hsl(0 0% 98%);

  --muted: hsl(0 0% 14.9%);
  --muted-foreground: hsl(0 0% 63.9%);

  --popover: hsl(0 0% 3.9%);
  --popover-foreground: hsl(0 0% 98%);

  --card: hsl(0 0% 3.9%);
  --card-foreground: hsl(0 0% 98%);

  --border: hsl(0 0% 14.9%);
  --input: hsl(0 0% 14.9%);

  --primary: hsl(0 72.2% 50.6%);
  --primary-foreground: hsl(0 85.7% 97.3%);

  --secondary: hsl(0 0% 14.9%);
  --secondary-foreground: hsl(0 0% 98%);

  --accent: hsl(0 0% 14.9%);
  --accent-foreground: hsl(0 0% 98%);

  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(0 0% 98%);

  --ring: hsl(0 72.2% 50.6%);
}

.theme-rose {
  --background: hsl(0 0% 100%);
  --foreground: hsl(240 10% 3.9%);

  --muted: hsl(240 4.8% 95.9%);
  --muted-foreground: hsl(240 3.8% 46.1%);

  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(240 10% 3.9%);

  --card: hsl(0 0% 100%);
  --card-foreground: hsl(240 10% 3.9%);

  --border: hsl(240 5.9% 90%);
  --input: hsl(240 5.9% 90%);

  --primary: hsl(346.8 77.2% 49.8%);
  --primary-foreground: hsl(355.7 100% 97.3%);

  --secondary: hsl(240 4.8% 95.9%);
  --secondary-foreground: hsl(240 5.9% 10%);

  --accent: hsl(240 4.8% 95.9%);
  --accent-foreground: hsl(240 5.9% 10%);

  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(0 0% 98%);

  --ring: hsl(346.8 77.2% 49.8%);
}

.theme-rose.dark {
  --background: hsl(20 14.3% 4.1%);
  --foreground: hsl(0 0% 95%);

  --muted: hsl(0 0% 15%);
  --muted-foreground: hsl(240 5% 64.9%);

  --popover: hsl(0 0% 9%);
  --popover-foreground: hsl(0 0% 95%);

  --card: hsl(24 9.8% 10%);
  --card-foreground: hsl(0 0% 95%);

  --border: hsl(240 3.7% 15.9%);
  --input: hsl(240 3.7% 15.9%);

  --primary: hsl(346.8 77.2% 49.8%);
  --primary-foreground: hsl(355.7 100% 97.3%);

  --secondary: hsl(240 3.7% 15.9%);
  --secondary-foreground: hsl(0 0% 98%);

  --accent: hsl(12 6.5% 15.1%);
  --accent-foreground: hsl(0 0% 98%);

  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(0 85.7% 97.3%);

  --ring: hsl(346.8 77.2% 49.8%);
}

.theme-orange {
  --background: hsl(0 0% 100%);
  --foreground: hsl(20 14.3% 4.1%);

  --muted: hsl(60 4.8% 95.9%);
  --muted-foreground: hsl(25 5.3% 44.7%);

  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(20 14.3% 4.1%);

  --card: hsl(0 0% 100%);
  --card-foreground: hsl(20 14.3% 4.1%);

  --border: hsl(20 5.9% 90%);
  --input: hsl(20 5.9% 90%);

  --primary: hsl(24.6 95% 53.1%);
  --primary-foreground: hsl(60 9.1% 97.8%);

  --secondary: hsl(60 4.8% 95.9%);
  --secondary-foreground: hsl(24 9.8% 10%);

  --accent: hsl(60 4.8% 95.9%);
  --accent-foreground: hsl(24 9.8% 10%);

  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(60 9.1% 97.8%);

  --ring: hsl(24.6 95% 53.1%);
}

.theme-orange.dark {
  --background: hsl(20 14.3% 4.1%);
  --foreground: hsl(60 9.1% 97.8%);

  --muted: hsl(12 6.5% 15.1%);
  --muted-foreground: hsl(24 5.4% 63.9%);

  --popover: hsl(20 14.3% 4.1%);
  --popover-foreground: hsl(60 9.1% 97.8%);

  --card: hsl(20 14.3% 4.1%);
  --card-foreground: hsl(60 9.1% 97.8%);

  --border: hsl(12 6.5% 15.1%);
  --input: hsl(12 6.5% 15.1%);

  --primary: hsl(20.5 90.2% 48.2%);
  --primary-foreground: hsl(60 9.1% 97.8%);

  --secondary: hsl(12 6.5% 15.1%);
  --secondary-foreground: hsl(60 9.1% 97.8%);

  --accent: hsl(12 6.5% 15.1%);
  --accent-foreground: hsl(60 9.1% 97.8%);

  --destructive: hsl(0 72.2% 50.6%);
  --destructive-foreground: hsl(60 9.1% 97.8%);

  --ring: hsl(20.5 90.2% 48.2%);
}

.theme-green {
  --background: hsl(0 0% 100%);
  --foreground: hsl(240 10% 3.9%);

  --muted: hsl(240 4.8% 95.9%);
  --muted-foreground: hsl(240 3.8% 46.1%);

  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(240 10% 3.9%);

  --card: hsl(0 0% 100%);
  --card-foreground: hsl(240 10% 3.9%);

  --border: hsl(240 5.9% 90%);
  --input: hsl(240 5.9% 90%);

  --primary: hsl(142.1 76.2% 36.3%);
  --primary-foreground: hsl(355.7 100% 97.3%);

  --secondary: hsl(240 4.8% 95.9%);
  --secondary-foreground: hsl(240 5.9% 10%);

  --accent: hsl(240 4.8% 95.9%);
  --accent-foreground: hsl(240 5.9% 10%);

  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(0 0% 98%);

  --ring: hsl(142.1 76.2% 36.3%);
}

.theme-green.dark {
  --background: hsl(20 14.3% 4.1%);
  --foreground: hsl(0 0% 95%);

  --muted: hsl(0 0% 15%);
  --muted-foreground: hsl(240 5% 64.9%);

  --popover: hsl(0 0% 9%);
  --popover-foreground: hsl(0 0% 95%);

  --card: hsl(24 9.8% 10%);
  --card-foreground: hsl(0 0% 95%);

  --border: hsl(240 3.7% 15.9%);
  --input: hsl(240 3.7% 15.9%);

  --primary: hsl(142.1 70.6% 45.3%);
  --primary-foreground: hsl(144.9 80.4% 10%);

  --secondary: hsl(240 3.7% 15.9%);
  --secondary-foreground: hsl(0 0% 98%);

  --accent: hsl(12 6.5% 15.1%);
  --accent-foreground: hsl(0 0% 98%);

  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(0 85.7% 97.3%);

  --ring: hsl(142.4 71.8% 29.2%);
}

.theme-blue {
  --background: hsl(0 0% 100%);
  --foreground: hsl(222.2 84% 4.9%);

  --muted: hsl(210 40% 96.1%);
  --muted-foreground: hsl(215.4 16.3% 46.9%);

  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(222.2 84% 4.9%);

  --card: hsl(0 0% 100%);
  --card-foreground: hsl(222.2 84% 4.9%);

  --border: hsl(214.3 31.8% 91.4%);
  --input: hsl(214.3 31.8% 91.4%);

  --primary: hsl(221.2 83.2% 53.3%);
  --primary-foreground: hsl(210 40% 98%);

  --secondary: hsl(210 40% 96.1%);
  --secondary-foreground: hsl(222.2 47.4% 11.2%);

  --accent: hsl(210 40% 96.1%);
  --accent-foreground: hsl(222.2 47.4% 11.2%);

  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(210 40% 98%);

  --ring: hsl(221.2 83.2% 53.3%);
}

.theme-blue.dark {
  --background: hsl(222.2 84% 4.9%);
  --foreground: hsl(210 40% 98%);

  --muted: hsl(217.2 32.6% 17.5%);
  --muted-foreground: hsl(215 20.2% 65.1%);

  --popover: hsl(222.2 84% 4.9%);
  --popover-foreground: hsl(210 40% 98%);

  --card: hsl(222.2 84% 4.9%);
  --card-foreground: hsl(210 40% 98%);

  --border: hsl(217.2 32.6% 17.5%);
  --input: hsl(217.2 32.6% 17.5%);

  --primary: hsl(217.2 91.2% 59.8%);
  --primary-foreground: hsl(222.2 47.4% 11.2%);

  --secondary: hsl(217.2 32.6% 17.5%);
  --secondary-foreground: hsl(210 40% 98%);

  --accent: hsl(217.2 32.6% 17.5%);
  --accent-foreground: hsl(210 40% 98%);

  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(210 40% 98%);

  --ring: hsl(224.3 76.3% 48%);
}

.theme-yellow {
  --background: hsl(0 0% 100%);
  --foreground: hsl(20 14.3% 4.1%);

  --muted: hsl(60 4.8% 95.9%);
  --muted-foreground: hsl(25 5.3% 44.7%);

  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(20 14.3% 4.1%);

  --card: hsl(0 0% 100%);
  --card-foreground: hsl(20 14.3% 4.1%);

  --border: hsl(20 5.9% 90%);
  --input: hsl(20 5.9% 90%);

  --primary: hsl(47.9 95.8% 53.1%);
  --primary-foreground: hsl(26 83.3% 14.1%);

  --secondary: hsl(60 4.8% 95.9%);
  --secondary-foreground: hsl(24 9.8% 10%);

  --accent: hsl(60 4.8% 95.9%);
  --accent-foreground: hsl(24 9.8% 10%);

  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(60 9.1% 97.8%);

  --ring: hsl(20 14.3% 4.1%);
}

.theme-yellow.dark {
  --background: hsl(20 14.3% 4.1%);
  --foreground: hsl(60 9.1% 97.8%);

  --muted: hsl(12 6.5% 15.1%);
  --muted-foreground: hsl(24 5.4% 63.9%);

  --popover: hsl(20 14.3% 4.1%);
  --popover-foreground: hsl(60 9.1% 97.8%);

  --card: hsl(20 14.3% 4.1%);
  --card-foreground: hsl(60 9.1% 97.8%);

  --border: hsl(12 6.5% 15.1%);
  --input: hsl(12 6.5% 15.1%);

  --primary: hsl(47.9 95.8% 53.1%);
  --primary-foreground: hsl(26 83.3% 14.1%);

  --secondary: hsl(12 6.5% 15.1%);
  --secondary-foreground: hsl(60 9.1% 97.8%);

  --accent: hsl(12 6.5% 15.1%);
  --accent-foreground: hsl(60 9.1% 97.8%);

  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(60 9.1% 97.8%);

  --ring: hsl(35.5 91.7% 32.9%);
}

.theme-violet {
  --background: hsl(0 0% 100%);
  --foreground: hsl(224 71.4% 4.1%);

  --muted: hsl(220 14.3% 95.9%);
  --muted-foreground: hsl(220 8.9% 46.1%);

  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(224 71.4% 4.1%);

  --card: hsl(0 0% 100%);
  --card-foreground: hsl(224 71.4% 4.1%);

  --border: hsl(220 13% 91%);
  --input: hsl(220 13% 91%);

  --primary: hsl(262.1 83.3% 57.8%);
  --primary-foreground: hsl(210 20% 98%);

  --secondary: hsl(220 14.3% 95.9%);
  --secondary-foreground: hsl(220.9 39.3% 11%);

  --accent: hsl(220 14.3% 95.9%);
  --accent-foreground: hsl(220.9 39.3% 11%);

  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(210 20% 98%);

  --ring: hsl(262.1 83.3% 57.8%);
}

.theme-violet.dark {
  --background: hsl(224 71.4% 4.1%);
  --foreground: hsl(210 20% 98%);

  --muted: hsl(215 27.9% 16.9%);
  --muted-foreground: hsl(217.9 10.6% 64.9%);

  --popover: hsl(224 71.4% 4.1%);
  --popover-foreground: hsl(210 20% 98%);

  --card: hsl(224 71.4% 4.1%);
  --card-foreground: hsl(210 20% 98%);

  --border: hsl(215 27.9% 16.9%);
  --input: hsl(215 27.9% 16.9%);

  --primary: hsl(263.4 70% 50.4%);
  --primary-foreground: hsl(210 20% 98%);

  --secondary: hsl(215 27.9% 16.9%);
  --secondary-foreground: hsl(210 20% 98%);

  --accent: hsl(215 27.9% 16.9%);
  --accent-foreground: hsl(210 20% 98%);

  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(210 20% 98%);

  --ring: hsl(263.4 70% 50.4%);
}
