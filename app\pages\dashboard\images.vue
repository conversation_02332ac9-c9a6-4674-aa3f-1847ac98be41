<script setup lang="ts">
import { useFileDialog } from '@vueuse/core'

const serverUploading = ref(false)
const clientUploading = ref(false)
const deleteLoading = ref({})

type AspectRatioType = 'portrait' | 'square'
const aspectRatio = ref<AspectRatioType>('portrait')

const { data: images, status, refresh } = await useFetch('/api/images')

const { open: openServerUpload, onChange: onServerFileChange } = useFileDialog({
  accept: 'image/*',
  multiple: false,
})

const { open: openClientUpload, onChange: onClientFileChange } = useFileDialog({
  accept: 'image/*',
  multiple: false,
})

onServerFileChange(async (files) => {
  if (!files.length) return

  const file = files[0]
  const formData = new FormData()
  formData.append('image', file)

  try {
    serverUploading.value = true
    await $fetch('/api/images/upload', {
      method: 'POST',
      body: formData,
    })
    await refresh()
    toast.success('Image uploaded successfully')
  }
  catch (error) {
    toast.error(error.data?.message || 'Failed to upload image')
  }
  finally {
    serverUploading.value = false
  }
})

onClientFileChange(async (files) => {
  if (!files.length) return

  const file = files[0]
  try {
    clientUploading.value = true
    const { uploadUrl, uploadName, url } = await $fetch('/api/images/get-client-upload-url', {
      method: 'POST',
      body: {
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
      },
    })
    const res = await $fetch.raw(uploadUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': file.type,
      },
      body: file,
    })
    if (res.ok) {
      await $fetch('/api/images', {
        method: 'POST',
        body: {
          name: uploadName,
          url,
        },
      })
      toast.success('Image uploaded successfully')
      await refresh()
    }
  }
  catch (error) {
    toast.error(error.data?.message || 'Failed to upload image')
  }
  finally {
    clientUploading.value = false
  }
})

async function deleteImage(imageId) {
  try {
    deleteLoading.value = { ...deleteLoading.value, [imageId]: true }
    await $fetch(`/api/images/${imageId}`, { method: 'DELETE' })
    await refresh()
    toast.success('Image deleted successfully')
  }
  catch (error) {
    toast.error(error.data?.message || 'Failed to delete image')
  }
  finally {
    deleteLoading.value = { ...deleteLoading.value, [imageId]: false }
  }
}
</script>

<template>
  <section
    id="images"
    class="mx-auto w-full max-w-full px-6 md:max-w-5xl"
  >
    <div class="mt-8 flex justify-between border-b pb-6">
      <div class="space-y-1">
        <h1 class="flex items-center text-[28px] font-bold leading-[34px] tracking-[-0.416px]">
          Images
          <Icon
            v-if="status === 'pending'"
            name="svg-spinners:3-dots-fade"
            class="ml-2 size-5"
          />
        </h1>
        <p class="text-sm text-muted-foreground">
          Your personal playlists. Updated daily.
        </p>
      </div>
      <div class="flex items-center gap-x-2">
        <Button
          variant="default"
          class="h-8"
          @click="openServerUpload"
        >
          <Icon
            v-if="serverUploading"
            name="svg-spinners:3-dots-fade"
            class="mr-2 size-5"
          />
          <Icon
            v-else
            name="lucide:plus"
            class="mr-2 h-5 opacity-60"
          />
          Server Upload
        </Button>
        <span class="text-xs">OR</span>
        <Button
          variant="default"
          class="h-8"
          @click="openClientUpload"
        >
          <Icon
            v-if="clientUploading"
            name="svg-spinners:3-dots-fade"
            class="mr-2 size-5"
          />
          <Icon
            v-else
            name="lucide:plus"
            class="mr-2 h-5 opacity-60"
          />
          Client Upload
        </Button>
      </div>
    </div>

    <div class="relative mt-6 grid grid-cols-2 gap-6 sm:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4">
      <div
        v-for="image in images"
        :key="image.id"
        class="group relative overflow-hidden rounded-md"
      >
        <Image
          :src="image.url"
          :alt="image.name"
          :class="cn(
            'object-cover object-center h-40 w-full transition-all hover:scale-105',
            aspectRatio === 'portrait' ? 'aspect-[3/4]' : 'aspect-square',
          )"
        />
        <div class="mt-2 text-sm">
          <h3 class="truncate font-medium leading-7">
            {{ image.name }}
          </h3>
          <p class="text-xs text-muted-foreground">
            {{ image.createdAt }}
          </p>
        </div>
        <Button
          variant="secondary"
          size="sm"
          class="absolute right-2 top-2 opacity-20 group-hover:opacity-100"
          @click="deleteImage(image.id)"
        >
          <Icon
            v-if="deleteLoading[image.id]"
            name="svg-spinners:3-dots-fade"
          />
          <Icon
            v-else
            name="lucide:trash-2"
          />
        </Button>
      </div>
    </div>
  </section>
</template>
