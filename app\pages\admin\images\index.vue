<script setup lang="ts">
import type { Prisma } from '@prisma/client'

import {
  FlexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useVueTable,
  type SortingState,
  type ColumnDef,
  type VisibilityState,
} from '@tanstack/vue-table'
import { format as dateFormat } from 'date-fns'
import RowActions from './components/RowActions.vue'
import Show from './components/Show.vue'
import { Checkbox } from '@/components/ui/checkbox'
import { Icon } from '#components'

definePageMeta({
  title: 'Images List',
})

const sorting = ref<SortingState>([])
const orderBy = computed(() => {
  const result: Prisma.UserOrderByWithRelationInput = {}
  let curr: any = result
  sorting.value[0]?.id.split(/[_.]/g).map((key, index, arr) => {
    curr = curr[key]
      = index == arr.length - 1
        ? sorting.value[0]?.desc
          ? 'desc'
          : 'asc'
        : {}
  })

  return result
})

const page = ref(1)
const pageSize = ref(10)
const search = ref('')
const debouncedSearch = refDebounced(search, 450)
const { data, status } = await useFetch('/api/admin/images', {
  query: computed(() => ({
    page: page.value,
    pageSize: pageSize.value,
    search: debouncedSearch.value,
    orderBy: orderBy.value,
  })),
  watch: [sorting, debouncedSearch],
})

watch(pageSize, () => {
  page.value = 1
})

const currentImage = ref(null)
const showOpen = ref(false)
const openShow = (image) => {
  currentImage.value = image
  showOpen.value = true
}

const remove = (target) => {
  const newImages = data.value.images.filter(image => image.id !== target.id)
  data.value = { ...data.value, images: newImages }
}

const columns = ref([
  {
    id: 'select',
    header: ({ table }) => h(Checkbox, {
      'checked': table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate'),
      'onUpdate:checked': value => table.toggleAllPageRowsSelected(!!value),
      'ariaLabel': 'Select all',
    }),
    cell: ({ row }) => h(Checkbox, {
      'checked': row.getIsSelected(),
      'onUpdate:checked': value => row.toggleSelected(!!value),
      'ariaLabel': 'Select row',
    }),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'id',
    cell: ({ row }) => h('div', { class: 'truncate lowercase w-[65px]' }, row.getValue('id')),
  },
  {
    accessorKey: 'userId',
    cell: ({ row }) => h('div', { class: 'truncate lowercase w-[65px]' }, row.getValue('userId')),
  },
  {
    accessorKey: 'name',
  },
  {
    accessorKey: 'url',
    cell: ({ row }) => h('img', { src: row.getValue('url'), class: 'w-[65px]' }),
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => {
      return h('span', {
        variant: 'ghost',
        class: 'flex items-center cursor-pointer',
        onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
      }, ['Created At', h(Icon, { name: 'lucide:chevrons-up-down', class: 'ml-2 h-4 w-4' })])
    },
    cell: ({ row }) => { return row.getValue('createdAt') && dateFormat(row.getValue('createdAt'), 'yyyy-MM-dd hh:mm:ss') },
  },
  {
    accessorKey: 'updatedAt',
    cell: ({ row }) => { return row.getValue('updatedAt') && dateFormat(row.getValue('updatedAt'), 'yyyy-MM-dd hh:mm:ss') },
  },
  {
    id: 'actions',
    header: ({ column }) => {
      return h('div', {
        class: 'flex justify-end mr-1',
      }, '')
    },
    enableHiding: false,
    cell: ({ row }) => h(RowActions, { row, onShow: openShow, onRemove: remove }),
  },
])

const columnVisibility = ref<VisibilityState>({})
const rowSelection = ref({})
const table = useVueTable({
  get data() {
    return data.value?.images ?? []
  },
  get columns() {
    return columns.value as ColumnDef<any>[]
  },
  getCoreRowModel: getCoreRowModel(),
  getSortedRowModel: getSortedRowModel(),
  getFilteredRowModel: getFilteredRowModel(),
  manualPagination: true,
  // sorting
  manualSorting: true,
  onSortingChange: updaterOrValue => valueUpdater(updaterOrValue, sorting),
  // onColumnFiltersChange: updaterOrValue => {
  //   console.warn('------onColumnFiltersChange-----')
  //   valueUpdater(updaterOrValue, columnFilters)
  // },
  onColumnVisibilityChange: updaterOrValue => valueUpdater(updaterOrValue, columnVisibility),
  onRowSelectionChange: updaterOrValue => valueUpdater(updaterOrValue, rowSelection),
  state: {
    get sorting() { return sorting.value },
    // get columnFilters() { return columnFilters.value },
    get columnVisibility() { return columnVisibility.value },
    get rowSelection() { return rowSelection.value },
  },
})

const isFiltered = computed(() => table.getState().columnFilters.length > 0)
</script>

<template>
  <div class="flex justify-between py-4">
    <h1 class="flex items-center text-[28px] font-bold leading-[34px] tracking-[-0.416px]">
      Images
      <Icon
        v-if="status === 'pending'"
        name="svg-spinners:3-dots-fade"
        class="ml-2 size-5"
      />
    </h1>
    <Button
      variant="secondary"
      class="h-8"
    >
      <Icon
        name="radix-icons:plus-circled"
        class="mr-2 h-5 opacity-60"
      /> Create
    </Button>
  </div>
  <div class="flex items-center justify-between">
    <div class="flex flex-1 items-center space-x-2">
      <Input
        placeholder="Filter name..."
        :model-value="(table.getColumn('name')?.getFilterValue() as string) ?? ''"
        class="h-8 w-[150px] lg:w-[250px]"
        @input="table.getColumn('name')?.setFilterValue($event.target.value)"
      />
      <Button
        v-if="isFiltered"
        variant="ghost"
        class="h-8 px-2 lg:px-3"
        @click="table.resetColumnFilters()"
      >
        Reset
        <Icon name="radix-icons:cross-2" />
      </Button>
    </div>
    <AdminViewOptions :table="table" />
  </div>

  <div class="rounded-md border">
    <Table>
      <TableHeader>
        <TableRow
          v-for="headerGroup in table.getHeaderGroups()"
          :key="headerGroup.id"
        >
          <TableHead
            v-for="header in headerGroup.headers"
            :key="header.id"
            class="[&:has([role=checkbox])]:pl-3"
          >
            <FlexRender
              v-if="!header.isPlaceholder"
              :render="header.column.columnDef.header"
              :props="header.getContext()"
            />
          </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        <template v-if="table.getRowModel().rows?.length">
          <template
            v-for="row in table.getRowModel().rows"
            :key="row.id"
          >
            <TableRow
              :data-state="row.getIsSelected() && 'selected'"
            >
              <TableCell
                v-for="cell in row.getVisibleCells()"
                :key="cell.id"
                class="[&:has([role=checkbox])]:pl-3"
              >
                <FlexRender
                  :render="cell.column.columnDef.cell"
                  :props="cell.getContext()"
                />
              </TableCell>
            </TableRow>
          </template>
        </template>

        <TableRow v-else>
          <TableCell
            :colspan="columns.length"
            class="h-24 text-center"
          >
            No results.
          </TableCell>
        </TableRow>
      </TableBody>
    </Table>
  </div>

  <AdminPager
    :table="table"
    :page="page"
    :page-size="pageSize"
    :total="data?.total ?? 0"
    @update-page="(value) => (page = value)"
    @update-page-size="(value) => (pageSize = value)"
  />

  <Show
    :open="showOpen"
    :image="currentImage"
    @open-change="(open) => (showOpen = open)"
  />
</template>
