<script setup lang="ts">
</script>

<template>
  <div class="mx-auto grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:gap-y-20 lg:mx-0 lg:max-w-none lg:grid-cols-2">
    <div class="flex flex-col justify-center lg:pr-8">
      <!-- <p class="text-muted-foreground">Deploy faster</p> -->
      <h3 class="text-5xl leading-tight tracking-tight">
        <span class="font-gradient inline bg-clip-text text-transparent">Rule your boards</span>
      </h3>
      <p class="mt-6 text-2xl text-muted-foreground">
        Lorem ipsum, dolor sit amet consectetur adipisicing elit.
      </p>
      <dl class="mt-10 max-w-xl space-y-8 lg:max-w-none">
        <div class="relative  text-muted-foreground">
          <dt class="inline font-semibold text-foreground">
            Push to deploy.
          </dt>
          <dd class="inline">
            Lorem ipsum, dolor sit amet consectetur adipisicing elit. Maiores impedit perferendis suscipit eaque, iste dolor cupiditate blanditiis ratione.
          </dd>
        </div>
        <div class="relative text-muted-foreground">
          <dt class="inline font-semibold text-foreground">
            SSL certificates.
          </dt>
          <dd class="inline">
            Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo.
          </dd>
        </div>
        <div class="relative text-muted-foreground">
          <dt class="inline font-semibold text-foreground">
            Database backups.
          </dt>
          <dd class="inline">
            Ac tincidunt sapien vehicula erat auctor pellentesque rhoncus. Et magna sit morbi lobortis.
          </dd>
        </div>
      </dl>
    </div>
    <div class="relative h-[500px] overflow-hidden rounded-2xl bg-gradient-to-b from-slate-700 to-slate-400 pl-8 pt-8">
      <div class="overflow-hidden">
        <Image
          src="/screenshots/admin_tasks.png"
          alt="Product screenshot"
          class="w-[50rem] max-w-none"
        />
      </div>
    </div>
  </div>
</template>
