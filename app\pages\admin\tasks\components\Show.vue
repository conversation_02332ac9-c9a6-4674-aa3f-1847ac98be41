<script setup lang="ts">
const props = defineProps<{
  open: boolean
  task: object | null
}>()

const emit = defineEmits<{
  openChange: [open: boolean]
}>()

const jsonstr = { id: 1, name: 'A green door', price: 12.50, tags: ['home', 'green'] }
</script>

<template>
  <Dialog
    :open="open"
    @update:open="(value: boolean) => emit('openChange', value)"
  >
    <DialogContent>
      <DialogHeader>
        <DialogTitle>Task Data</DialogTitle>
        <DialogDescription>
          Display the raw database table data.
        </DialogDescription>
      </DialogHeader>

      <pre class="mt-4 overflow-auto rounded border bg-card p-4 sm:max-h-[600px]">{{ JSON.stringify(task, null, 2) }}</pre>
    </DialogContent>
  </Dialog>
</template>
