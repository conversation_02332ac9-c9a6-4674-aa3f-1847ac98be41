<script setup lang="ts">
import { format as dateFormat } from 'date-fns'

const route = useRoute()

const { data: page } = await useAsyncData(route.path, () => queryContent(route.path).findOne())
if (!page.value) {
  throw createError({ statusCode: 404, statusMessage: 'Page not found', fatal: true })
}

useSeoMeta({
  title: page.value.title,
  ogTitle: `${page.value.title}`,
  description: page.value.description,
  ogDescription: page.value.description,
})
</script>

<template>
  <div class="container mb-20">
    <div class="px-4 sm:px-6 lg:px-8">
      <div class="relative mx-auto max-w-[37.5rem] pb-12 pt-20 text-center">
        <h1 class="font-gradient text-4xl font-semibold tracking-tight sm:text-5xl">
          {{ page.title }}
        </h1>
        <p class="mt-4 leading-7 text-muted-foreground">
          Last updated on {{ dateFormat(page.date, "MMMM dd, yyyy") }}
        </p>
      </div>
    </div>
    <ContentRenderer
      :value="page"
      class="prose prose-zinc mx-auto mt-6 max-w-3xl dark:prose-invert"
    />
  </div>
</template>
