import { z } from 'zod'

export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)

  const { name, image } = await readValidatedBody(event, body =>
    z.object({
      name: z.string().min(1).optional(),
      image: z.string().optional(),
    }).parse(body),
  )

  const updatedUser = await db.user.update({
    where: {
      id: user.id,
    },
    data: {
      name,
      image,
    },
  })

  const transformedUser = sanitizeUser(updatedUser)

  await setUserSession(event, { user: transformedUser })

  return transformedUser
})
