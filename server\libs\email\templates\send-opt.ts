export function sendOneTimePasswordTemplate({ email, brand, link, optCode }: { email: string, brand: string, link: string, optCode: string }) {
  if (!email || !brand || !link || !optCode) {
    throw new Error('Email Template Missing required parameters: email, brand, and link and optCode are required.')
  }

  return `
    <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
    <html dir="ltr" lang="en">
      <head>
        <meta content="text/html; charset=UTF-8" http-equiv="Content-Type" />
        <meta name="x-apple-disable-message-reformatting" />
      </head>
      <div style="display:none;overflow:hidden;line-height:1px;opacity:0;max-height:0;max-width:0">
        ${brand} - Sign-In code
        <div></div>
      </div>

      <body style="background-color:#000000!important;color:#cccccc!important;margin:0;padding:0;width:100%;">
        <table width="100%" cellpadding="0" cellspacing="0" style="width:100%;margin:0;padding:0;background-color:#0c0a09!important;color:#f2f2f2!important;margin:0;padding:0;width:100%">
          <tbody>
            <tr>
              <td align="center" style="">
                <table width="100%" cellpadding="0" cellspacing="0" style="width:100%;margin:0;padding:0;">
                  <tbody>
                    <tr>
                      <td style="padding:25px 0;text-align:center;" align="center">
                        <a href="${link}" style="color:#f2f2f2;font-size:18px;font-weight:bold;text-decoration:none;" target="_blank">${brand}</a>
                      </td>
                    </tr>
                    <tr>
                      <td width="100%" style="width:100%;margin:0;padding:0;">
                        <table align="center" width="570" cellpadding="0" cellspacing="0" style="width:570px;margin:0 auto;padding:0;">
                          <tbody>
                            <tr>
                              <td style="padding:35px;">
                                <h1 style="margin-bottom: 16px;color:#f2f2f2;font-size:18px;font-weight:bold;text-align:left;">Hi ${email},</h1>
                                <p style="margin-bottom: 16px;font-size:16px;line-height:1.5em;text-align:left;">
                                  We received a sign-in request for the email address ${email}. To get started, please enter the code <strong>${optCode}</strong> in your open browser window.
                                </p>
                                  
                                <table align="center" width="100%" cellpadding="0" cellspacing="0" style="width:100%;margin:30px auto;padding:0;text-align:center;">
                                  <tbody>
                                    <tr>
                                      <td align="center" style="">
                                        <p style="">
                                          <a href="${link}" style="
                                              color:#ffffff;
                                              display:inline-block;
                                              width:200px;
                                              background-color:#006239;
                                              border-radius:3px;
                                              font-size:24px;
                                              line-height:45px;
                                              text-align:center;
                                              text-decoration:none;
                                              font-weight: 400;
                                              letter-spacing:6px;" 
                                              target="_blank"
                                            >
                                            ${ optCode }
                                          </a>
                                        </p>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                                <hr style="border-width:1px;border-style:solid;border-color:rgb(234,234,234);margin-top:26px;margin-bottom:26px;margin-left:0px;margin-right:0px;width:100%;border:none;border-top:1px solid #27272a" />
                                <table>
                                  <tbody>
                                    <tr>
                                      <td style="">
                                        <p style="margin-bottom: 16px;font-size:12px;line-height:1.5em;text-align:left;">
                                          This code expires in 10 minutes.
                                        </p>
                                        <p style="margin-bottom: 16px;font-size:12px;line-height:1.5em;text-align:left;">
                                          If you didn’t request for ${brand}, you can safely ignore this email. Someone else might have typed your email address by mistake.
                                        </p>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                    </tr>
                    <tr>
                      <td style="">
                        <table align="center" width="570" cellpadding="0" cellspacing="0" style="width:570px;margin:0 auto;padding:0;text-align:center;color:#a1a1aa;">
                          <tbody>
                            <tr>
                              <td style="padding:35px;">
                                <p style="margin-top:0;font-size:12px;line-height:1.5em;text-align:center;">© 2025 ${brand}. All rights reserved.</p>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </td>
            </tr>
          </tbody>
        </table>
      </body>
    </html>
  `
}
