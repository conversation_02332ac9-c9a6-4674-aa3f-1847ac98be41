// If you need to enable this feature, please uncomment this code and install the required package.

/*
import mixpanel from 'mixpanel-browser'

export const useAnalytics = () => {
  const init = () => {
    if (!import.meta.client || !import.meta.env.VITE_MIXPANEL_TOKEN) {
      return
    }

    const mixpanelToken = import.meta.env.VITE_MIXPANEL_TOKEN

    mixpanel.init(mixpanelToken, {
      track_pageview: true,
      persistence: 'localStorage',
    })
  }

  const trackEvent = (event: string, data?: Record<string, any>) => {
    mixpanel.track(event, data)
  }

  return {
    init,
    trackEvent,
  }
}
*/
