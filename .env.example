# Database
# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings
DATABASE_URL="file:./dev.db"
MIGRATION_DATABASE_URL="file:./dev.db"

# Better Auth
# https://www.better-auth.com/docs/installation 点击生成密钥
BETTER_AUTH_SECRET=
BETTER_AUTH_URL=http://localhost:3000

# Email
RESEND_API_KEY=

# nuxt-auth-utils
NUXT_SESSION_PASSWORD=
# nuxt-auth-utils - github
NUXT_OAUTH_GITHUB_CLIENT_ID=
NUXT_OAUTH_GITHUB_CLIENT_SECRET=
# nuxt-auth-utils - google
NUXT_OAUTH_GOOGLE_CLIENT_ID=
NUXT_OAUTH_GOOGLE_CLIENT_SECRET=
# nuxt-auth-utils - twitter
NUXT_OAUTH_X_CLIENT_ID=
NUXT_OAUTH_X_CLIENT_SECRET=

# Payment - Stripe
STRIPE_SECRET_KEY=
STRIPE_PUBLIC_KEY=
STRIPE_WEBHOOK_SECRET=

# Payment - lemonsqueezy
LEMONSQUEEZY_API_KEY=
LEMONSQUEEZY_STORE_ID=
LEMONSQUEEZY_WEBHOOK_SECRET=

# Payment - Paddle
VITE_PADDLE_ENV=sandbox # or `production`
VITE_PADDLE_CLIENT_TOKEN=
PADDLE_API_KEY=
PADDLE_NOTIFICATION_WEBHOOK_SECRET=

# Storage - s3
S3_ACCESS_KEY_ID=
S3_SECRET_ACCESS_KEY=
S3_BUCKET_NAME=
S3_ENDPOINT=
S3_PUBLIC_ACCESS_URL=

# Analytics
VITE_GOOGLE_ANALYTICS_ID=
