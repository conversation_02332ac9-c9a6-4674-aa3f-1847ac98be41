<script setup lang="ts">
definePageMeta({
  layout: false,
})

const route = useRoute()

const catsQuery = queryContent('docs')

const { data: doc } = await useAsyncData(route.path, () => queryContent(route.path).findOne())
if (!doc.value) {
  throw createError({ statusCode: 404, statusMessage: 'Page not found', fatal: true })
}

useSeoMeta({
  title: doc.value.title,
  ogTitle: doc.value.title,
  description: doc.value.description,
  ogDescription: doc.value.description,
})
const queryBuilder = queryContent('docs')
const { data: docNavigation } = await useAsyncData('navigation', () => fetchContentNavigation(queryBuilder))
const secondaryNav = transformNavigation(docNavigation.value[0]?.children)

function transformNavigation(data) {
  function transformItem(item) {
    if (item.children && item.children.length) {
      return {
        title: item.title,
        items: item.children.map(child => transformItem(child)), // 递归处理子项
      }
    }
    return {
      title: item.title,
      href: item._path,
    }
  }
  return data.map(item => transformItem(item))
}
</script>

<template>
  <Header
    class="border-b"
    :mobile-secondary-nav="secondaryNav"
  />
  <div class="container flex-1 items-start md:grid md:grid-cols-[220px_minmax(0,1fr)] md:gap-6 lg:grid-cols-[240px_minmax(0,1fr)] lg:gap-10">
    <aside
      class="fixed top-[59px] z-30 -ml-2 hidden h-[calc(100vh-59px)] w-full shrink-0 overflow-y-auto border-r md:sticky md:block"
    >
      <ScrollArea
        orientation="vertical"
        class="relative h-full overflow-hidden py-6 pr-6 lg:py-8"
        type="auto"
      >
        <div class="w-full">
          <ContentNavigation
            v-slot="{ navigation }"
            :query="catsQuery"
          >
            <div
              v-for="children in navigation[0]?.children"
              :key="children.title"
              class="pb-4"
            >
              <h4
                v-if="children.children?.length"
                class="mb-1 rounded-md px-2 py-1 text-sm font-semibold"
              >
                {{ children.title }}

                <span
                  v-if="children.label"
                  class="ml-2 rounded-md bg-[#adfa1d] px-1.5 py-0.5 text-xs font-normal leading-none text-[#000000] no-underline group-hover:no-underline"
                >
                  {{ children.label }}
                </span>
              </h4>

              <NuxtLink
                v-else
                :href="children._path"
                class="group flex w-full items-center rounded-md border border-transparent px-2 py-1 text-sm text-muted-foreground hover:underline"
                :class="{
                  '!font-semibold !text-foreground': route.path === `${children._path}.html`,
                }"
              >
                {{ children.title }}

                <span
                  v-if="children.label"
                  class="ml-2 rounded-md bg-[#adfa1d] px-1.5 py-0.5 text-xs leading-none text-[#000000] no-underline group-hover:no-underline"
                >
                  {{ children.label }}
                </span>
              </NuxtLink>

              <div
                v-for="docItem in children.children"
                :key="docItem.title"
                class="grid grid-flow-row auto-rows-max text-sm"
              >
                <NuxtLink
                  v-if="docItem._path"
                  :href="docItem._path"
                  class="group flex w-full items-center rounded-md border border-transparent px-2 py-1 text-muted-foreground hover:underline"
                  :class="{
                    '!font-semibold !text-foreground': route.path === `${docItem._path}.html`,
                  }"
                >
                  {{ docItem.title }}

                  <span
                    v-if="docItem.label"
                    class="ml-2 rounded-md bg-[#adfa1d] px-1.5 py-0.5 text-xs leading-none text-[#000000] no-underline group-hover:no-underline"
                  >
                    {{ docItem.label }}
                  </span>
                </NuxtLink>
              </div>
            </div>
          </ContentNavigation>
        </div>
      </ScrollArea>
    </aside>

    <main class="relative lg:gap-10 xl:grid xl:grid-cols-[1fr_300px]">
      <div class="mx-auto w-full min-w-0 py-6">
        <div class="block xl:hidden">
          <DocTableOfContent :tree="doc.body!.toc!.links" />
        </div>

        <DocBreadcrumb class="mb-4" />
        <div class="mb-12 space-y-2">
          <div class="flex items-center space-x-4">
            <h1 class="scroll-m-20 text-4xl font-bold tracking-tight">
              {{ doc.title }}
            </h1>
            <span
              v-if="doc.label"
              class="ml-2 rounded-md bg-[#adfa1d] px-1.5 py-0.5 text-xs leading-none text-[#000000] no-underline group-hover:no-underline"
            >
              {{ doc.label }}
            </span>
          </div>
          <p class="text-lg text-muted-foreground">
            {{ doc.description }}
          </p>
        </div>

        <div class="prose prose-zinc mb-20 dark:prose-invert">
          <ContentRenderer :value="doc" />
        </div>
      </div>

      <div class="hidden text-sm xl:block">
        <div class="sticky top-[59px] h-[calc(100vh-59px)] overflow-hidden pt-6">
          <DocTableOfContent :tree="doc.body!.toc!.links" />
        </div>
      </div>
    </main>
  </div>
</template>
