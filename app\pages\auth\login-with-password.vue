<script setup lang="ts">
definePageMeta({
  title: 'Login',
  layout: false,
})
</script>

<template>
  <Button
    as-child
    variant="ghost"
    class="absolute left-0 top-6 rounded-full text-sm font-semibold text-muted-foreground md:left-6"
  >
    <NuxtLink href="/">
      <Icon
        name="radix-icons:caret-left"
        class="mr-1"
      /> Home
    </NuxtLink>
  </Button>
  <div class="flex h-screen items-center justify-center">
    <main class="mx-auto min-h-[490px] w-full max-w-[450px]">
      <h1 class="mb-1.5 mt-8 text-center text-xl font-bold tracking-[-0.16px] sm:text-left">
        Login
      </h1>
      <p class="mb-8 text-center text-base font-normal text-muted-foreground sm:text-left">
        Don't have an account?
        <NuxtLink
          class="text-primary hover:underline"
          href="/auth/signup"
        >
          Sign up
        </NuxtLink>
      </p>
      <AuthLoginPasswordForm />
      <Separator
        class="my-8 font-semibold"
        label="OR"
      />
      <div class="mb-4 flex flex-col items-center gap-4">
        <Button
          class="w-full"
          size="lg"
          as-child
          variant="outline"
          type="button"
        >
          <a
            class="text-muted-foreground"
            href="/api/auth/github"
          ><Icon
            name="ri:github-fill"
            class="mr-2 size-5"
          /> Login with Github</a>
        </Button>
        <Button
          class="w-full"
          size="lg"
          as-child
          variant="outline"
          type="button"
        >
          <a
            class="text-muted-foreground"
            href="/api/auth/google"
          ><Icon
            name="custom:ri-google-fill"
            class="mr-2 size-5"
          /> Login with Google</a>
        </Button>
      </div>
      <p class="mt-8 text-xs text-muted-foreground">
        By signing up, you agree to our
        <NuxtLink
          class="text-primary hover:underline"
          target="_blank"
          href="/legal/terms-of-service"
        >
          terms
        </NuxtLink>,
        <NuxtLink
          class="text-primary hover:underline"
          target="_blank"
          href="/legal/acceptable-use"
        >
          acceptable use
        </NuxtLink>, and
        <NuxtLink
          class="text-primary hover:underline"
          target="_blank"
          href="/legal/privacy-policy"
        >
          privacy policy
        </NuxtLink>
      </p>
    </main>
  </div>
</template>
