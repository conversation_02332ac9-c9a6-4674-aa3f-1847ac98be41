import { auth } from '@@/auth'

export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)

  if (user.role !== 'ADMIN') {
    throw createError({
      statusCode: 403,
      statusMessage: 'Forbidden',
      message: 'Admin role required'
    })
  }

  const query = getQuery(event)
  const page = Number(query.page || 1)
  const pageSize = Number(query.pageSize || 10)
  const search = String(query.search || '')
  const filter = String(query.filter || 'all')
  const orderBy = JSON.parse(String(query.orderBy || '{"createdAt":"desc"}'))

  const searchConditions = query.search
    ? {
        OR: [
          {
            name: {
              contains: String(query.search),
            },
            email: {
              contains: String(query.search),
            },
          },
        ],
      }
    : null

  const where = {
    ...searchConditions,
  }

  const [users, total] = await db.$transaction([
    db.user.findMany({
      where,
      skip: (page - 1) * pageSize || 0,
      take: pageSize || 20,
      orderBy: {
        createdAt: 'desc',
      },
    }),
    db.user.count({
      where,
    }),
  ])

  return { users, total }
})
