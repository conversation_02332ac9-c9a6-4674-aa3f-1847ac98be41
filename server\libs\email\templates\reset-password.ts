export function resetPasswordTemplate({ brand, link }: { brand: string, link: string }) {
  if (!brand || !link) {
    throw new Error('Email Template Missing required parameters: brand, and link are required.')
  }

  return `
    <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
    <html dir="ltr" lang="en">
      <head>
        <meta content="text/html; charset=UTF-8" http-equiv="Content-Type" />
        <meta name="x-apple-disable-message-reformatting" />
      </head>
      <div style="display:none;overflow:hidden;line-height:1px;opacity:0;max-height:0;max-width:0">
        ${brand} - Reset Password
        <div></div>
      </div>

      <body style="background-color:#000000!important;color:#cccccc!important;margin:0;padding:0;width:100%;">
        <table align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="max-width:480px;border-radius:5px;margin:0 auto 40px;padding:20px">
          <tbody>
            <tr style="width:100%">
              <td>
                <h2 style="margin-bottom: 16px;color:#f2f2f2;font-size:18px;font-weight:bold;text-align:left;">Hi ${brand},</h2>
                <p style="font-size:24px;line-height:40px;margin:0 0 20px;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI','Roboto','Oxygen','Ubuntu','Cantarell','Fira Sans','Droid Sans','Helvetica Neue',sans-serif;color:#ffffff;font-weight:600">Reset your password</p>
                <p style="font-size:14px;line-height:24px;margin:0 0 40px;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI','Roboto','Oxygen','Ubuntu','Cantarell','Fira Sans','Droid Sans','Helvetica Neue',sans-serif;color:#aaaaaa">Follow the button to reset the password for your user.</p>
                <table align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="margin:0 0 40px">
                  <tbody>
                    <tr>
                      <td>
                        <a href="${link}" 
                          style="
                            line-height:100%;
                            text-decoration:none;
                            display:inline-block;
                            max-width:100%;
                            font-family:-apple-system,BlinkMacSystemFont,'Segoe UI','Roboto','Oxygen','Ubuntu','Cantarell','Fira Sans','Droid Sans','Helvetica Neue',sans-serif;
                            background-color:#ffffff;
                            border-radius:8px;
                            color:#0b0b0f;
                            font-size:14px;
                            font-weight:600;
                            text-align:center;
                            width:200px;padding:16px 20px 16px 20px" 
                          target="_blank" 
                          >
                            <span style="max-width:100%;display:inline-block;line-height:120%">Reset Password</span>
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>
                <p style="font-size:14px;line-height:24px;margin:0 0 40px;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI','Roboto','Oxygen','Ubuntu','Cantarell','Fira Sans','Droid Sans','Helvetica Neue',sans-serif;color:#aaaaaa">
                  If you don't want to change your password or didn't request this, just ignore and delete this message.
                </p>
              </td>
            </tr>
          </tbody>
        </table>
      </body>
    </html>
  `
}
