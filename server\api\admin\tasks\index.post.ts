import { z } from 'zod'

export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)

  const { title, content } = await readValidatedBody(event, body =>
    z.object({
      title: z.string().min(1).max(120),
      content: z.string(),
    }).parse(body),
  )

  const task = await db.task.create({
    data: {
      title,
      content,
      userId: user.id,
    },
  })

  return task
})
