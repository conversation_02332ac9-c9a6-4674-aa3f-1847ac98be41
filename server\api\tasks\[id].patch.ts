import { z } from 'zod'

export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)

  const taskId = getRouterParam(event, 'id')
  const schema = z.object({
    title: z.string().min(1).max(240).optional(),
    content: z.string().optional(),
  })

  const { title, content } = await readValidatedBody(event, body =>
    schema.parse(body),
  )

  const task = await db.task.update({
    where: {
      id: taskId,
      userId: user.id,
    },
    data: {
      title,
      content,
    },
  })

  return task
})
