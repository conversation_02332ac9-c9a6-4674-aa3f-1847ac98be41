import { useSSE } from '@/composables/useSSE'

export default defineEventHandler(async (event) => {
  const { sendToUser } = useSSE()
  const query = getQuery(event)
  // const body = await readBody(event)
  // return {
  //   message: 'from POST /api/test',
  //   query: query,
  //   body,
  // }
  // 获取 SSE 端点中的 sendEvent 函数
  // sseHooks.callHook('sse:event', "RESYNC");
  // sseHooks.callHook('sse:event', query.id);
  sendToUser(query.id as string, 'test')
})
