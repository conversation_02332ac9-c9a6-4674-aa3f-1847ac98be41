export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)

  const sharedId = getRouterParam(event, 'id')

  const shared = await db.shared.findUnique({
    where: {
      id: sharedId,
      userId: user.id,
    },
  })

  if (!shared) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Shared not found',
    })
  }

  return shared
})
