import type { PaddleOptions } from '@paddle/paddle-node-sdk'
import { Environment, LogLevel, Paddle } from '@paddle/paddle-node-sdk'
import type {
  Product,
  ProductVariant,
  CheckoutOptions,
  CustomerPortalOptions,
  SubscriptionStatus,
  Transaction,
} from '../types'

const paddleInstance: Paddle | null = null

export function getPaddleInstance() {
  if (paddleInstance) {
    return paddleInstance
  }

  const paddleOptions: PaddleOptions = {
    environment: (process.env.VITE_PADDLE_ENV as Environment) ?? Environment.sandbox,
    logLevel: LogLevel.error,
  }

  if (!process.env.PADDLE_API_KEY) {
    console.error('Paddle API key is missing')
  }

  return new Paddle(process.env.PADDLE_API_KEY!, paddleOptions)
}

export const getProducts = async () => {
  const paddleInstance = getPaddleInstance()

  // const productCollection = paddleInstance.products.list({
  //   include: ['prices']
  // }).next()
  // firstPage
  // const firstPage = await productCollection.next()
  // console.log("First page data", firstPage)

  const prices = await paddleInstance.prices.list({
    include: ['product'],
  }).next()

  const plansMap: Record<string, Product> = {}

  prices.forEach((price) => {
    const product = price.product as unknown as Product

    if (!plansMap[product.id]) {
      plansMap[product.id] = {
        id: product.id,
        name: product.name,
        description: product.description,
        storeId: null,
        variants: [],
      }
    }

    const variant: ProductVariant = {
      id: price.id,
      interval: price.billingCycle?.interval ?? 'year',
      interval_count: price.billingCycle?.frequency ?? 0,
      price: Number(price.unitPrice?.amount ?? 0),
      currency: String(price.unitPrice?.currencyCode ?? 'USD'),
    }

    plansMap[product.id].variants.push(variant)
  })

  // return Object.values(plansMap).filter((plan) => plan.variants.length > 0);
  return Object.values(plansMap)
}

export const createCheckoutLink = async (options: CheckoutOptions): Promise<string> => {
  //  Paddle does not support checkout links;
  //  It only supports 'inline' and 'overlay' using Paddle.js on the client side.
  const { baseUrl } = useRuntimeConfig().public
  const url = `${baseUrl}/paddle_checkout/${options.variantId}`

  return url
}

export const createCustomerPortalLink = async (options: CustomerPortalOptions): Promise<string> => {
  const paddleInstance = getPaddleInstance()

  // The paddle-node-sdk currently does not provide a method to obtain the customer portal link.
  const data = await paddleInstance.subscriptions.get(String(options.id))

  let url: string
  switch (options.flow) {
    case 'cancel':
      url = data.managementUrls?.cancel || ''
      break
    default:
      url = data.managementUrls?.updatePaymentMethod || ''
      break
  }
  return url
}
