import { watch } from 'vue'
import { authClient } from '~/utils/auth-client'
import { notifyExtension } from '~/utils/extension'

export default defineNuxtPlugin(() => {
  try {
    const session = authClient.useSession()

    watch(session, (newValue, oldValue) => {
      // 只在有实际变化时才通知
      if (shouldNotifyExtension(newValue, oldValue)) {
        console.log('User changed, notifying extension')
        notifyExtension('userChanged')
      }
    }, {
      deep: true,
      immediate: false // 避免初始化时就触发
    })
  } catch (error) {
    console.error('❌ Error setting up auth listener:', error)
  }
})

/**
 * 判断是否应该通知扩展端
 * 只在以下情况下通知 - 后续完善，多通知几次也没什么影响：
 * 0. 初始化时（页面刷新）
 * 1. 用户登录状态改变（登录/登出）
 * 2. 用户属性改变（如 email、name 等）
 * 3. Session 过期
 */
function shouldNotifyExtension(newValue: any, oldValue: any): boolean {
  const oldUser = oldValue?.data?.user
  const newUser = newValue?.data?.user
  const oldSession = oldValue?.data?.session
  const newSession = newValue?.data?.session

  // 1. 登录状态改变：从无用户到有用户，或从有用户到无用户
  if ((!oldUser && newUser) || (oldUser && !newUser)) {
    return true
  }

  // 2. 用户属性改变：用户 ID 改变或用户信息改变
  if (oldUser && newUser) {
    // 简单粗暴：直接比较整个用户对象的 JSON 字符串
    if (JSON.stringify(oldUser) !== JSON.stringify(newUser)) {
      return true
    }
  }

  // 3. Session 过期：有 session 变成无 session
  if (oldSession && !newSession && newUser) {
    return true // Session 过期但用户信息还在
  }

  return false
}
