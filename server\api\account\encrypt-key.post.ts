import { z } from 'zod'

export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)

  const { encryptSalt, encryptKey, encryptIv, encryptAlgo } = await readValidatedBody(event, body =>
    z.object({
      encryptSalt: z.string(),
      encryptKey: z.string(),
      encryptIv: z.string(),
      encryptAlgo: z.string(),
    }).parse(body),
  )

  const updatedUser = await db.user.update({
    where: {
      id: user.id,
    },
    data: {
      encryptSalt,
      encryptKey,
      encryptIv,
      encryptAlgo,
    },
  })

  return {
    encryptSalt: updatedUser.encryptSalt,
    encryptKey: updatedUser.encryptKey,
    encryptIv: updatedUser.encryptIv,
    encryptAlgo: updatedUser.encryptAlgo,
  }
})
