<script lang="ts" setup>
const { themes } = useAppConfig()
const colorMode = useColorMode()
const themeStore = useThemeStore()
</script>

<template>
  <div class="p-4">
    <div class="grid space-y-1">
      <h1 class="font-semibold text-foreground">
        Customize
      </h1>
      <p class="text-xs text-muted-foreground">
        Pick a style and color for your components.
      </p>
    </div>
    <div class="space-y-1.5 pt-6">
      <Label
        for="color"
        class="text-xs"
      > Color </Label>
      <div class="grid grid-cols-3 gap-2 py-1.5">
        <Button
          v-for="(theme, index) in themes"
          :key="index"
          variant="outline"
          class="h-8 justify-start px-3"
          :class="
            theme === themeStore.theme
              ? 'border-foreground border-2'
              : ''
          "
          @click="themeStore.setTheme(theme)"
        >
          <span :class="`theme-${theme} ${colorMode.value}`">
            <span
              class="flex size-5 items-center justify-center rounded-full bg-primary"
            >
              <Icon
                v-if="theme === themeStore.theme"
                name="radix-icons:check"
                class="size-3 text-primary-foreground"
              />
            </span>
          </span>
          <span class="ml-2 text-xs capitalize">
            {{ theme }}
          </span>
        </Button>
      </div>
    </div>
    <div class="space-y-1.5 pt-6">
      <Label
        for="radius"
        class="text-xs"
      > Radius </Label>
      <div class="grid grid-cols-5 gap-2 py-1.5">
        <Button
          v-for="(r, index) in themeStore.RADII"
          :key="index"
          variant="outline"
          class="h-8 justify-center px-3"
          :class="
            r === themeStore.radius
              ? 'border-foreground border-2'
              : ''
          "
          @click="themeStore.setRadius(r)"
        >
          <span class="text-xs">
            {{ r }}
          </span>
        </Button>
      </div>
    </div>
    <div class="space-y-1.5 pt-6">
      <Label
        for="theme"
        class="text-xs"
      > Theme </Label>

      <div class="flex space-x-2 py-1.5">
        <Button
          class="h-8"
          variant="outline"
          :class="{ 'border-2 border-foreground': colorMode.value !== 'dark' }"
          @click="colorMode.preference = 'light'"
        >
          <Icon
            name="radix-icons:sun"
            class="mr-2 size-4"
          />
          <span class="text-xs">Light</span>
        </Button>
        <Button
          class="h-8"
          variant="outline"
          :class="{ 'border-2 border-foreground': colorMode.value !== 'light' }"
          @click="colorMode.preference = 'dark'"
        >
          <Icon
            name="radix-icons:moon"
            class="mr-2 size-4"
          />
          <span class="text-xs">Dark</span>
        </Button>
      </div>
    </div>
  </div>
</template>
