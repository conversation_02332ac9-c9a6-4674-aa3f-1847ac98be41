export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)
  const id = getRouterParam(event, 'id')

  const shared = await db.shared.findUnique({
    where: {
      id,
    },
  })

  if (!shared) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Shared not found'
    })
  }

  // 解析 bookmarkData 以获取所有 bookmarkId
  let bookmarkIds: string[] = []
  try {
    const bookmarkData = typeof shared.bookmarkData === 'string'
      ? JSON.parse(shared.bookmarkData)
      : shared.bookmarkData

    // 递归提取所有书签ID
    function extractBookmarkIds(nodes: any[]): string[] {
      const ids: string[] = []
      for (const node of nodes) {
        ids.push(node.id)
        if (node.children && node.children.length > 0) {
          ids.push(...extractBookmarkIds(node.children))
        }
      }
      return ids
    }

    if (Array.isArray(bookmarkData)) {
      bookmarkIds = extractBookmarkIds(bookmarkData)
    }
  } catch (e) {
    console.error('解析书签数据失败:', e)
  }

  // 获取对应的 extra 数据
  const extras = bookmarkIds.length > 0 ? await db.extra.findMany({
    where: {
      userId: user.id,
      bookmarkId: {
        in: bookmarkIds
      },
      deleted: false
    }
  }) : []

  // 获取对应的 tag 数据
  const tags = bookmarkIds.length > 0 ? await db.tag.findMany({
    where: {
      userId: user.id,
      deleted: false
    }
  }) : []

  return {
    ...shared,
    extras,
    tags
  }
})
