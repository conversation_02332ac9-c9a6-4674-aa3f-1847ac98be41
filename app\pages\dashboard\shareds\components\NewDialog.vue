<script setup lang="ts">
import { z } from 'zod'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'

const props = defineProps<{
  open: boolean
  parentId: string | null
}>()

const emit = defineEmits<{
  openChange: [open: boolean]
  saved: [bookmark: object | null]
}>()

const bookmarkSchema = toTypedSchema(
  z.object({
    title: z.string().min(2),
    url: z.string().optional(),
    category: z.string(),
    parentId: z.string().optional(),
  }),
)
const form = useForm({
  validationSchema: bookmarkSchema,
})

const loading = ref(false)
const onSubmit = form.handleSubmit(async (values) => {
  try {
    loading.value = true
    values.category = 'shared'
    values.parentId = props.parentId
    const updatedBookmark = await $fetch(`/api/bookmarks`, { method: 'POST', body: values })
    emit('saved', updatedBookmark)
    emit('openChange', false)
    toast('Bookmark created')
  }
  catch (error) {
    console.warn('Error: ', error)
    loading.value = false
    toast.error(error.data ? error.data.statusMessage : 'An unknown error occurred')
  }
})
</script>

<template>
  <Dialog
    :open="open"
    @update:open="(value: boolean) => emit('openChange', value)"
  >
    <DialogContent class="sm:max-w-[425px]">
      <DialogHeader>
        <DialogTitle>New Bookmark</DialogTitle>
        <DialogDescription>
          Make new bookmark here. Click save when you're done.
        </DialogDescription>
      </DialogHeader>

      <form
        id="dialogForm"
        class="flex flex-col items-stretch gap-4"
        :validation-schema="bookmarkSchema"
        @submit="onSubmit"
      >
        <FormField
          v-slot="{ componentField }"
          name="title"
        >
          <FormItem>
            <FormControl>
              <Input
                type="text"
                placeholder="title"
                v-bind="componentField"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
        <FormField
          v-slot="{ componentField }"
          name="url"
        >
          <FormItem>
            <FormLabel
              class="text-muted-foreground"
              for="url"
            >
              URL
            </FormLabel>
            <FormControl>
              <Textarea v-bind="componentField" />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
      </form>

      <DialogFooter>
        <Button
          type="submit"
          form="dialogForm"
        >
          Create
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
