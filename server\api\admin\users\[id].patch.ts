import { z } from 'zod'

export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)

  const userId = getRouterParam(event, 'id')
  const { name, image } = await readValidatedBody(event, body =>
    z.object({
      name: z.string().min(1).optional(),
      image: z.string().optional()
    }).parse(body),
  )

  const updatedUser = await db.user.update({
    where: {
      id: userId,
    },
    data: {
      name,
      image,
    },
  })

  return updatedUser
})
