---
description: Setting up starter and running the local development server.
---

This guide will walk you through setting `<script setup>` up Acme. We will go through the process of cloning the project, installing dependencies, setting up your database and running the local development server.

### Installation
<Steps>

##### Clone the repository
Run the following command on your local environment:

```bash
git clone https://github.com/acme.git project-name
```

##### Install dependencies
```bash
cd project-name
# It will install all packages
pnpm install
```

##### Init Database
```bash
pnpm db:push
```

##### Generate Prisma Client
```bash
pnpm db:generate
```
</Steps>

#### Development
Start the dev server:

```bash
pnpm dev
```

You should now be able to access the application with live reload at http://127.0.0.1:7000.
