<script setup lang="ts">

</script>

<template>
  <h2 class="mx-auto text-center text-4xl leading-tight tracking-tight">
    Our story
  </h2>
  <p class="mx-auto mt-12 max-w-[480px] text-base font-normal tracking-wide text-muted-foreground md:text-[1.125rem] md:leading-normal">
    Acme was born out of a simple idea in 2023: to <strong class="font-normal text-foreground">revolutionize</strong> the way teams manage their tasks. We were <strong class="font-normal text-foreground">fed up with the clunky</strong>, inefficient tools that made collaboration a chore rather than a breeze.
    <br><br>
    As we delved deeper into the world of task management, it became apparent that the challenges we faced were <strong class="font-normal text-foreground">just the beginning</strong>. We discovered that the real struggle was not just about organizing tasks, but about streamlining the entire workflow to <strong class="font-normal text-foreground">boost productivity</strong>. This insight led us to innovate beyond the ordinary.
    <br><br>
    In 2024, we launched our task management platform and were thrilled to be part of <strong class="font-normal text-foreground">Y Combinator's</strong> winter cohort. We're building something extraordinary here, and we can't wait to grow and evolve with you.
  </p>
</template>
