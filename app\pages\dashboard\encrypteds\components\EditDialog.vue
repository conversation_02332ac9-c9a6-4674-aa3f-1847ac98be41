<script setup lang="ts">
import { z } from 'zod'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'

const props = defineProps<{
  open: boolean
  bookmark: any | null
}>()

const emit = defineEmits<{
  openChange: [open: boolean]
  saved: [bookmark: object | null]
}>()

const bookmarkSchema = toTypedSchema(
  z.object({
    title: z.string().min(2),
    content: z.string(),
  }),
)
const form = useForm({
  validationSchema: bookmarkSchema,
})

const loading = ref(false)
const onSubmit = form.handleSubmit(async (values) => {
  try {
    loading.value = true
    const updatedBookmark = await $fetch(`/api/bookmarks/${props.bookmark.id}`, { method: 'PATCH', body: values })
    emit('openChange', false)
    toast('Bookmark saved')
  }
  catch (error) {
    console.warn('Error: ', error)
    loading.value = false
    toast.error(error.data ? error.data.statusMessage : 'An unknown error occurred')
  }
})
</script>

<template>
  <Dialog
    :open="open"
    @update:open="(value: boolean) => emit('openChange', value)"
  >
    <DialogContent class="sm:max-w-[425px]">
      <DialogHeader>
        <DialogTitle>Edit bookmark</DialogTitle>
        <DialogDescription>
          Make changes to bookmark here. Click save when you're done.
        </DialogDescription>
      </DialogHeader>

      <form
        id="dialogForm"
        class="flex flex-col items-stretch gap-4"
        :validation-schema="bookmarkSchema"
        @submit="onSubmit"
      >
        <FormField
          v-slot="{ componentField }"
          name="title"
          :value="bookmark.title"
        >
          <FormItem>
            <FormControl>
              <Input
                type="text"
                placeholder="title"
                v-bind="componentField"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
        <FormField
          v-slot="{ componentField }"
          name="content"
          :value="bookmark.content"
        >
          <FormItem>
            <FormLabel
              class="text-muted-foreground"
              for="content"
            >
              Content
            </FormLabel>
            <FormControl>
              <Textarea v-bind="componentField" />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
      </form>

      <DialogFooter>
        <Button
          type="submit"
          form="dialogForm"
        >
          Save changes
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
