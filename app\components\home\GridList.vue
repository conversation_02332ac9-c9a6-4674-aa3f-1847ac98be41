<script setup lang="ts">
const blocks = [{
  title: 'Efficient Design',
  description: 'A streamlined approach to web development that simplifies the process and reduces the time to market, allowing for quicker deployment of your application.',
  icon: 'lucide:door-closed',
  tag: { name: 'Nuxt', class: 'bg-green-500/20 text-green-500' },
},
{
  title: 'Modular System',
  description: 'Easily integrate components and plugins to create a tailor-made solution that fits your project\'s unique requirements, enhancing flexibility and maintainability.',
  icon: 'lucide:disc-3',
  tag: { name: 'Modular', class: 'bg-green-500/20 text-green-500' },
},
{
  title: 'SEO Friendly',
  description: 'Optimized for search engines right out of the box, ensuring that your content is easily discoverable by your target audience and improving your online visibility.',
  icon: 'lucide:binoculars',
  tag: { name: 'SEO', class: 'bg-blue-500/20 text-blue-500' },
},
{
  title: 'Performance Boost',
  description: 'Leverage the power of Vue.js and Nuxt.js to achieve faster page loads and a smoother user experience, which is crucial for retaining users and improving conversion rates.',
  icon: 'lucide:chart-bar-stacked',
  tag: { name: 'Speed', class: 'bg-green-500/20 text-green-500' },
},
{
  title: 'Scalability',
  description: 'Build large applications with ease, as Nuxt.js is designed to handle complex projects and scale efficiently as your user base grows, ensuring stability and performance.',
  icon: 'lucide:alarm-clock-plus',
  tag: { name: 'Scalable', class: 'bg-green-500/20 text-green-500' },
},
{
  title: 'Cross-Platform',
  description: 'Works seamlessly across all devices and platforms, ensuring that your application is accessible to a wide range of users and providing a consistent experience.',
  icon: 'lucide:dice-5',
  tag: { name: 'Cross-Platform', class: 'bg-rose-500/20 text-rose-500' },
},
{
  title: 'Real-Time Updates',
  description: 'Keep your users informed with real-time data updates, ensuring that they always have the most current information at their fingertips, which is essential for dynamic applications.',
  icon: 'lucide:database',
  tag: { name: 'Real-Time', class: 'bg-orange-500/20 text-orange-500' },
},
{
  title: 'Customizable Themes',
  description: 'Easily switch between themes or create your own to match your brand\'s identity, giving your application a unique and cohesive look that resonates with your audience.',
  icon: 'lucide:dna-off',
  tag: { name: 'Themes', class: 'bg-green-500/20 text-green-500' },
},
{
  title: 'Community Support',
  description: 'A vibrant community of developers and users is available to help you with any issues, providing a wealth of knowledge and support that can be invaluable for troubleshooting and learning.',
  icon: 'lucide:fan',
  tag: { name: 'Support', class: 'bg-green-500/20 text-green-500' },
},
]
</script>

<template>
  <div class="relative grid grid-cols-1 gap-x-20 sm:grid-cols-2 md:flex-row lg:grid-cols-3">
    <div
      v-for="(block, index) in blocks"
      :key="index"
      class="grid-list flex w-full flex-col text-pretty py-4 md:gap-2 md:pb-12 md:pt-9"
    >
      <Icon
        :name="block.icon"
        class="mb-2 size-5"
      />
      <h4 class="text-xl leading-[130%] md:leading-none">
        {{ block.title }}
      </h4>
      <p class="leading-[1.6] text-muted-foreground">
        {{ block.description }}
      </p>
    </div>
  </div>
</template>
