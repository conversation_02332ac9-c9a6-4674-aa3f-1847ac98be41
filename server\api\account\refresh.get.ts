import { auth } from '@@/auth'

export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)

  const updatedUser = await db.user.findUnique({
    where: {
      id: user.id,
    },
  })

  const transformedUser = sanitizeUser(updatedUser)

  // With better-auth, sessions are managed automatically
  // The next request will automatically get the updated user data
  // through our customSession plugin

  return transformedUser
})
