import { createCustomerPortalLink } from '@@/server/libs/payment'

const runtimeConfig = useRuntimeConfig()

export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)
  const query = getQuery(event)

  const subscription = await db.subscription.findUnique({
    where: {
      id: String(query.id),
      userId: user.id,
    },
  })

  const flow = query.flow || 'default'

  let flowData: any

  switch (flow) {
    case 'update-plan':
      flowData = {
        type: 'subscription_update',
        subscription_update: {
          subscription: subscription?.subId,
        },
      }
      break
    case 'update-payment-info':
      flowData = {
        type: 'payment_method_update',
      }
      break
    case 'cancel':
      flowData = {
        type: 'subscription_cancel',
        subscription_cancel: {
          subscription: subscription?.subId,
        },
      }
      break
    default:
      flowData = undefined
      break
  }

  const billingPortalLink = await createCustomerPortalLink({
    id: subscription?.subId,
    customerId: subscription?.customerId || '',
    redirectUrl: `${runtimeConfig.public.baseUrl}/dashboard/settings/billing`,
    flowData,
  })
  return billingPortalLink
})
