import { z } from 'zod'

export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)

  const updatedUser = await db.user.findUnique({
    where: { id: user.id },
  })

  if (!updatedUser) {
    throw createError({
      statusCode: 404,
      statusMessage: 'User not found',
    })
  }

  return {
    encryptSalt: updatedUser.encryptSalt,
    encryptKey: updatedUser.encryptKey,
    encryptIv: updatedUser.encryptIv,
    encryptAlgo: updatedUser.encryptAlgo
  }
})
