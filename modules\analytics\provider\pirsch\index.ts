// If you need to enable this feature, please uncomment this code and install the required package.

/*
import { useRuntimeConfig, useHead } from '#imports'

export const useAnalytics = () => {
  const init = () => {
    if (!import.meta.client || !import.meta.env.VITE_PIRSCH_CODE) {
      return
    }

    const pirschCode = import.meta.env.VITE_PIRSCH_CODE

    useHead({
      script: [
        {
          'defer': true,
          'key': 'analytics-pirsch',
          'src': 'https://api.pirsch.io/pirsch-extended.js',
          'id': 'pirschextendedjs',
          'data-code': pirschCode,
        },
      ],
    })
  }

  const trackEvent = (event: string, data?: Record<string, any>) => {
    if (typeof window === 'undefined' || !(window as any).pirsch) {
      return
    }

    (window as any).pirsch(event, {
      meta: data,
    })
  }

  return {
    init,
    trackEvent,
  }
}
*/
