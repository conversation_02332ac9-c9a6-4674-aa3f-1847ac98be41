import { useSSE } from '@/composables/useSSE'

export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)
  const { sseConnections } = useSSE()
  const eventStream = createEventStream(event)
  const query = getQuery(event)

  // 将新连接加入连接池
  sseConnections.set(user.id, eventStream)

  // sseHooks.hook('sse:event', async (action: string) => {
  //   console.warn('----- sse triger -----', query.id, action)
  //   if (query.id === action) {
  //     await eventStream.push(`${action}`)
  //   }
  // })

  // 连接关闭时清理
  eventStream.onClosed(async () => {
    if (user.id && typeof user.id === 'string') {
      sseConnections.delete(user.id) // 从连接池移除
    }
    await eventStream.close()
    console.warn('-------- sse close! ----------')
  })

  return eventStream.send()
})
