// If you need to enable this feature, please uncomment this code and install the required package.

/*
import { track, inject } from '@vercel/analytics'

export const useAnalytics = () => {
  const init = () => {
    if (!import.meta.client) {
      return
    }

    inject()
  }

  const trackEvent = (event: string, data?: Record<string, any>) => {
    track(event, data)
  }

  return {
    init,
    trackEvent,
  }
}
*/
