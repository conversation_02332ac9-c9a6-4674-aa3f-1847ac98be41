import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
  GetObjectCommand,
} from '@aws-sdk/client-s3'
import { getSignedUrl as getS3SignedUrl } from '@aws-sdk/s3-request-presigner'
import type { FileStorageService, FileUploadOptions } from '../types'

const runtimeConfig = useRuntimeConfig()

let s3Client: S3Client | null = null
const bucket = process.env.S3_BUCKET_NAME ?? ''
const s3PublicUrl = process.env.S3_PUBLIC_ACCESS_URL ?? ''

export const getPublicUrl = (filename: string): string => {
  return `${s3PublicUrl}/${filename}`
}

const getS3Client = (): S3Client => {
  if (s3Client) return s3Client

  if (!bucket) {
    throw new Error('Missing S3_BUCKET_NAME.')
  }

  const s3Endpoint = process.env.S3_ENDPOINT as string
  const s3AccessKeyId = process.env.S3_ACCESS_KEY_ID as string
  const s3SecretAccessKey = process.env.S3_SECRET_ACCESS_KEY as string
  if (!s3Endpoint || !s3AccessKeyId || !s3SecretAccessKey) {
    throw new Error('Missing S3 configuration. Please check your environment variables.')
  }

  s3Client = new S3Client({
    region: 'auto',
    endpoint: s3Endpoint,
    credentials: {
      accessKeyId: s3AccessKeyId,
      secretAccessKey: s3SecretAccessKey,
    },
  })

  return s3Client
}

export const upload = async ({ name, data }: FileUploadOptions): Promise<string> => {
  const s3Client = getS3Client()
  let body: Buffer | string
  if (data instanceof Blob) {
    body = Buffer.from(await data.arrayBuffer())
  }
  else if (Buffer.isBuffer(data)) {
    body = data
  }
  else if (typeof data === 'string') {
    body = data
  }
  else {
    throw new Error('Unsupported data type for upload')
  }

  const command = new PutObjectCommand({
    Bucket: bucket,
    Key: name,
    Body: body,
  })

  try {
    const res = await s3Client.send(command)
    return getPublicUrl(name)
  }
  catch (error) {
    console.error('Error uploading file:', error)
    throw new Error(`Failed to upload file to ${bucket}/${name}`)
  }
}

export const deleteObject = async (name: string): Promise<void> => {
  const s3Client = getS3Client()
  const command = new DeleteObjectCommand({
    Bucket: bucket,
    Key: name,
  })

  try {
    await s3Client.send(command)
  }
  catch (error) {
    console.error('Error deleting file:', error)
    throw new Error(`Failed to delete file from ${bucket}/${name}`)
  }
}

export const getClientUploadUrl = async (name: string, contentType: string, expiresIn: number = 3600): Promise<string> => {
  const s3Client = getS3Client()
  try {
    return await getS3SignedUrl(
      s3Client,
      new PutObjectCommand({
        Bucket: bucket,
        Key: name,
        ContentType: contentType,
      }),
      { expiresIn },
    )
  }
  catch (error) {
    console.error('Error generating client upload URL:', error)
    throw new Error(`Failed to generate client upload URL for ${bucket}/${name}`)
  }
}
