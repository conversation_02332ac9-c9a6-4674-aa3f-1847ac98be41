{"type": "module", "private": true, "version": "1.2.0", "scripts": {"build": "nuxi build", "prebuild": "npx prisma generate", "dev": "nuxi dev", "clean": "nuxi cleanup", "generate": "nuxi generate", "prepare": "nuxi prepare", "start": "node .output/server/index.mjs", "start:generate": "npx serve .output/public", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "nuxi typecheck", "db:generate": "prisma generate", "db:push": "prisma db push --skip-generate", "db:format": "prisma format", "db:studio": "prisma studio"}, "dependencies": {"@aws-sdk/client-s3": "3.437.0", "@aws-sdk/s3-request-presigner": "3.437.0", "@internationalized/date": "^3.5.6", "@lemonsqueezy/lemonsqueezy.js": "^3.3.1", "@paddle/paddle-js": "^1.3.2", "@paddle/paddle-node-sdk": "^1.9.1", "@supabase/supabase-js": "^2.45.6", "@tanstack/vue-table": "^8.20.5", "@unovis/ts": "^1.4.4", "@unovis/vue": "^1.4.4", "@vee-validate/zod": "^4.13.2", "@vueuse/core": "11.0.3", "@vueuse/nuxt": "11.0.3", "better-auth": "^1.3.4", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "4.1.0", "lucide-vue-next": "^0.487.0", "magic-string": "^0.30.14", "oslo": "1.2.1", "pinia-plugin-persistedstate": "^4.1.3", "stripe": "^16.12.0", "tw-animate-css": "^1.2.5", "vaul-vue": "0.4.1", "vee-validate": "4.12.8", "vue": "^3.5.13", "vue-sonner": "^1.2.3", "zod": "^3.24.2", "zod-prisma-types": "3.2.4"}, "devDependencies": {"@iconify-json/devicon-plain": "^1.2.4", "@iconify-json/lucide": "^1.2.8", "@iconify-json/radix-icons": "^1.2.1", "@nuxt/content": "2.13.2", "@nuxt/eslint": "1.6.0", "@nuxt/fonts": "^0.11.3", "@nuxt/icon": "1.13.0", "@nuxt/image": "1.10.0", "@nuxtjs/color-mode": "^3.5.1", "@pinia/nuxt": "^0.5.5", "@prisma/client": "6.5.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "4.1.11", "@types/node": "^20.16.11", "nuxt": "3.17.7", "pinia": "^2.2.4", "prisma": "6.5.0", "reka-ui": "^2.2.0", "shiki": "^1.22.0", "tailwind-merge": "^3.2.0", "tailwindcss": "4.1.11", "tailwindcss-animate": "^1.0.7", "typescript": "5.6.3", "vue-tsc": "2.1.10"}}