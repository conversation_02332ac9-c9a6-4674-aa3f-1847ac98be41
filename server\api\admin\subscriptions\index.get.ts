export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const page = Number(query.page || 1)
  const pageSize = Number(query.pageSize || 10)
  const search = String(query.search || '')
  const filter = String(query.filter || 'all')
  const orderBy = JSON.parse(String(query.orderBy || '{"createdAt":"desc"}'))

  const searchConditions = query.search
    ? {
        OR: [
          {
            subId: {
              contains: String(query.search),
            },
          },
        ],
      }
    : null

  const where = {
    ...searchConditions,
  }

  const [subscriptions, total] = await db.$transaction([
    db.subscription.findMany({
      where,
      skip: (page - 1) * pageSize || 0,
      take: pageSize || 20,
      orderBy,
    }),
    db.subscription.count({
      where,
    }),
  ])

  return { subscriptions, total }
})
