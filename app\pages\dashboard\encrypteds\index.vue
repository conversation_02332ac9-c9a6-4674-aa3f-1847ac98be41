<script setup lang="ts">
import { toast } from 'vue-sonner'
import { format as dateFormat } from 'date-fns'
import EditDialog from './components/EditDialog.vue'
import NewDialog from './components/NewDialog.vue'
import NewEncryptedFolderDialog from './components/NewEncryptedFolderDialog.vue'
import SetEncryptKeyDialog from '@/components/crypto/SetEncryptKeyDialog.vue'
import ResetEncryptKeyDialog from '@/components/crypto/ResetEncryptKeyDialog.vue'
import UnlockDialog from '@/components/crypto/UnlockDialog.vue'

import { useCrypto } from '@/composables/useCrypto'

const { isLocked, decryptBookmark } = useCrypto()

const { user, clear, loggedIn, fetch: refreshSession } = useUserSession()

const newEncryptedFolderDialogOpen = ref(false)

const {
  data: bookmarks,
  status,
  error,
  refresh,
} = await useFetch('/api/bookmarks?category=encrypted', {
  query: computed(() => ({
    category: 'encrypted',
    parentId: null,
  }))
})

const encryptedBookmarks = computedAsync(async () => {
  if (!bookmarks.value) return [];
  const encryptedBookmarks = bookmarks.value.filter(bookmark => bookmark.category === 'encrypted');
  
  if (isLocked.value) return encryptedBookmarks;

  // 如果存在，解密后返回
  const decryptedBookmarks = [];
  for (const bookmark of encryptedBookmarks) {
    const decrypted = await decryptBookmark(bookmark);
    if (decrypted) decryptedBookmarks.push(decrypted);
  }
  return decryptedBookmarks;
}, []); // 初始值为空数组

const search = ref('')

const loading = ref(false)
const editDialogOpen = ref(false)
const newDialogOpen = ref(false)
const setEncryptKeyDialogOpen = ref(false)
const resetEncryptKeyDialogOpen = ref(false)
const currentBookmark = ref(null)
const unlockDialogOpen = ref(false)

const editBookmark = (bookmark) => {
  currentBookmark.value = bookmark
  editDialogOpen.value = true
}

const deleteBookmark = async (target) => {
  try {
    await $fetch(`/api/bookmarks/${target.id}`, {
      method: 'DELETE',
    })
    refresh()
    toast.success('Bookmark deleted')
  }
  catch (error) {
    toast.error('Failed to delete bookmark')
  }
}
</script>

<template>
  <section
    id="bookmarks"
    class="mx-auto w-full max-w-full px-6 md:max-w-5xl"
  >
    <div class="flex justify-between py-8">
      <h1 class="flex items-center text-[28px] font-bold leading-[34px] tracking-[-0.416px]">
        Encrypted Bookmarks
        <Icon
          v-if="status === 'pending'"
          name="svg-spinners:3-dots-fade"
          class="ml-2 size-5"
        />
      </h1>

      <Button variant="ghost" size="icon" @click="newEncryptedFolderDialogOpen = true">
        <Icon name="lucide:plus" />
      </Button>

      <Button
        variant="default"
        class="h-8"
        @click="newDialogOpen = true"
      >
        <Icon
          name="radix-icons:plus-circled"
          class="mr-2 h-5 opacity-60"
        /> Create
      </Button>

      <Button
        variant="default"
        class="h-8"
        @click="setEncryptKeyDialogOpen = true"
      >
        <Icon
          name="radix-icons:plus-circled"
          class="mr-2 h-5 opacity-60"
        /> Set Encrypt Key
      </Button>

      <Button
        variant="default"
        class="h-8"
        @click="resetEncryptKeyDialogOpen = true"
      >
        <Icon
          name="radix-icons:plus-circled"
          class="mr-2 h-5 opacity-60"
        /> Reset Encrypt Key
      </Button>
    </div>
    <div class="mb-4 grid grid-cols-1 flex-col gap-3 sm:grid-cols-2 sm:gap-2">
      <div class="relative">
        <Input
          id="search"
          v-model="search"
          type="text"
          placeholder="Search title"
          class="pl-8"
        />
        <span class="absolute inset-y-0 start-0 flex items-center justify-center px-2">
          <Icon
            name="lucide:search"
            class="size-4 text-muted-foreground"
          />
        </span>
      </div>
    </div>

    <div
      v-if="bookmarks.length === 0"
      class="flex h-80 flex-col items-center justify-center gap-8 rounded-lg border p-6"
    >
      <div class="flex max-w-md flex-col gap-2 text-center">
        <h2 class="text-xl font-bold tracking-[-0.16px]">
          You haven't sent any emails yet
        </h2>
        <p class="text-sm text-muted-foreground">
          Once you start sending emails, you'll get detailed insight and content preview for every message.
        </p>
      </div>
      <Button
        class="font-semibold"
        @click="newDialogOpen = true"
      >
        Create Bookmark
      </Button>
    </div>

    <template v-else>
      <div class="mt-8 rounded border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead class="min-w-[200px]">
                Title
              </TableHead>
              <TableHead>Status</TableHead>
              <TableHead class="hidden md:table-cell">
                Completed at
              </TableHead>
              <TableHead class="hidden md:table-cell">
                Created at
              </TableHead>
              <TableHead>
                <span class="sr-only">Actions</span>
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow
              v-for="row in encryptedBookmarks"
              :key="row.id"
            >
              <TableCell class="font-medium">
                {{ row.title }}
              </TableCell>
              <TableCell>
                <Badge variant="outline">
                  Draft
                </Badge>
              </TableCell>
              <TableCell class="hidden md:table-cell">
                {{ row.dateAdded && dateFormat(row.dateAdded, "dd MMMM yyyy") }}
              </TableCell>
              <TableCell class="hidden md:table-cell">
                {{ dateFormat(row.createdAt, "dd MMM, hh:mm") }}
              </TableCell>
              <TableCell class="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger as-child>
                    <Button
                      aria-haspopup="true"
                      size="icon"
                      variant="ghost"
                    >
                      <Icon
                        name="lucide:more-vertical"
                        class="size-4"
                      />
                      <span class="sr-only">Toggle menu</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem @click="editBookmark(row)">
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem @click="deleteBookmark(row)">
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </template>

    <NewEncryptedFolderDialog
      :open="newEncryptedFolderDialogOpen"
      @open-change="(open) => (newEncryptedFolderDialogOpen = open)"
      @saved="refresh"
    />

    <EditDialog
      :open="editDialogOpen"
      :bookmark="currentBookmark"
      @open-change="(open) => (editDialogOpen = open)"
      @saved="refresh"
    />

    <NewDialog
      :open="newDialogOpen"
      @open-change="(open) => (newDialogOpen = open)"
      @saved="refresh"
    />

    <SetEncryptKeyDialog
      :open="setEncryptKeyDialogOpen"
      @open-change="(open) => (setEncryptKeyDialogOpen = open)"
      @saved="refresh"
    />

    <ResetEncryptKeyDialog
      :open="resetEncryptKeyDialogOpen"
      @open-change="(open) => (resetEncryptKeyDialogOpen = open)"
      @saved="refresh"
    />

    <UnlockDialog
      :open="unlockDialogOpen"
      @open-change="(open) => (unlockDialogOpen = open)"
    />
  </section>
</template>
