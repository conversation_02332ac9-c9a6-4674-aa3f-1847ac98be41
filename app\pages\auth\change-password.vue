<script setup lang="ts">
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { z } from 'zod'

definePageMeta({
  title: 'Change Password',
  layout: false,
})

const route = useRoute()
const { token } = route.query

const loading = ref(false)

const formSchema = toTypedSchema(
  z.object({
    password: z.string().min(6, 'Must be at least 6 characters'),
    passwordConfirmation: z.string().min(6, 'Must be at least 6 characters'),
  })
    .refine(data => data.password === data.passwordConfirmation, {
      message: 'Passwords must match',
      path: ['passwordConfirmation'],
    }),
)

const form = useForm({
  validationSchema: formSchema,
})

const onSubmit = form.handleSubmit(async (values) => {
  try {
    loading.value = true
    await $fetch('/api/auth/change-password', {
      method: 'PATCH',
      body: {
        password: values.password, code: token,
      },
    })
    toast.success('Password reset', {
      description: 'Password has been reset',
    })
    loading.value = false
    return navigateTo(`/auth/login`)
  }
  catch (error) {
    loading.value = false
    toast.error(error.data ? error.data.statusMessage : 'An unknown error occurred')
  }
})
</script>

<template>
  <Button
    as-child
    variant="ghost"
    class="absolute left-0 top-6 rounded-full text-sm font-semibold text-muted-foreground md:left-6"
  >
    <NuxtLink href="/">
      <Icon
        name="radix-icons:caret-left"
        class="mr-1"
      /> Home
    </NuxtLink>
  </Button>
  <div class="flex h-screen items-center justify-center">
    <main class="mx-auto min-h-[490px] w-full max-w-[450px]">
      <h1 class="mb-1.5 mt-8 text-center text-xl font-bold tracking-[-0.16px] sm:text-left">
        Set up a new password
      </h1>
      <p class="mb-8 text-center text-base font-normal text-muted-foreground sm:text-left">
        Your password must be different from your previous one.
      </p>
      <form
        class="flex flex-col items-stretch gap-4"
        @submit.prevent="onSubmit"
      >
        <FormField
          v-slot="{ componentField }"
          name="password"
        >
          <FormItem>
            <FormLabel
              class="text-muted-foreground"
              for="password"
            >
              New password
            </FormLabel>
            <FormControl>
              <Input
                type="password"
                placeholder="Enter your password"
                v-bind="componentField"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>

        <FormField
          v-slot="{ componentField }"
          name="passwordConfirmation"
        >
          <FormItem>
            <FormLabel
              class="text-muted-foreground"
              for="passwordConfirmation"
            >
              Confirm new password
            </FormLabel>
            <FormControl>
              <Input
                type="password"
                placeholder="Enter your password again"
                v-bind="componentField"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>

        <Button
          class="mt-2 w-full disabled:opacity-80"
          type="submit"
          size="lg"
          :disabled="loading"
        >
          <Icon
            v-show="loading"
            name="svg-spinners:3-dots-fade"
            class="mr-2 size-5"
          />
          Update Password
        </Button>
      </form>
    </main>
  </div>
</template>
