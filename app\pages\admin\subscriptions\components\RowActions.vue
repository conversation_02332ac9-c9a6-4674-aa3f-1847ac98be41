<script setup lang="ts">
import type { Row } from '@tanstack/vue-table'
import { computed } from 'vue'

interface DataTableRowActionsProps {
  row: Row<any>
}
const props = defineProps<DataTableRowActionsProps>()

const emit = defineEmits<{
  show: [subscription: object | null]
}>()

const subscription = computed(() => props.row.original)
</script>

<template>
  <div class="flex justify-end">
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <Button
          variant="ghost"
          class="flex size-8 p-0 data-[state=open]:bg-muted"
        >
          <Icon name="radix-icons:dots-horizontal" class="size-4" />
          <span class="sr-only">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        class="w-[160px]"
      >
        <DropdownMenuItem @click="$emit('show', subscription)">
          Show
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
</template>
