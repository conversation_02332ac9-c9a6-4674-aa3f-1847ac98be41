// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

// Mapping model names to tables or collections
// https://www.prisma.io/docs/orm/prisma-schema/data-model/models#mapping-model-names-to-tables-or-collections

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider     = "sqlite" // 'sqlite' or 'postgresql' - datasource must not use the env() function
  url          = env("DATABASE_URL")
  directUrl    = env("MIGRATION_DATABASE_URL") // if supabase
  relationMode = "prisma"
}

generator zod {
  provider         = "zod-prisma-types"
  output           = "./zod"
  createInputTypes = false
  addIncludeType   = false
  addSelectType    = false
}

// plan 逻辑设计：
// 用户同一时间只能属于一个套餐（free / pro / team）；「套餐」是互斥的，它只回答“用户付的是哪档钱”，试用期就是 15 天的过期日期
// 但 team 套餐拥有「pro 全部能力 + 团队业务」；
// trialExpiredAt
model User {
  id            String         @id @default(cuid())
  name          String
  email         String         @unique
  emailVerified Boolean        @default(false) @map("email_verified")
  image         String?
  active        Boolean        @default(true)
  role          String         @default("USER")
  plan          String         @default("FREE")
  updatedAt     DateTime       @updatedAt @map("updated_at")
  createdAt     DateTime       @default(now()) @map("created_at")
  encryptSalt   String?        @map("encrypt_salt")
  encryptKey    String?        @map("encrypt_key")
  encryptIv     String?        @map("encrypt_iv")
  encryptAlgo   String?        @map("encrypt_algo")
  sessions      Session[]
  accounts      Account[]
  subscriptions Subscription[]
  images        Image[]
  extras        Extra[]
  tags          Tag[]
  shareds       Shared[]
  sharedUsers   SharedUser[]
  snapshots     Snapshot[]

  @@index([email])
  @@map("users")
}

model Session {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  token     String
  expiresAt DateTime @map("expires_at")
  ipAddress String?  @map("ip_address")
  userAgent String?  @map("user_agent")
  updatedAt DateTime @updatedAt @map("updated_at")
  createdAt DateTime @default(now()) @map("created_at")
  user      User     @relation(references: [id], fields: [userId], onDelete: Cascade)

  @@index([userId])
  @@index([token])
  @@map("sessions")
}

model Account {
  id                    String    @id @default(cuid())
  userId                String    @map("user_id")
  accountId             String    @map("account_id")
  providerId            String    @map("provider_id")
  accessToken           String?   @map("access_token")
  refreshToken          String?   @map("refresh_token")
  accessTokenExpiresAt  DateTime? @map("access_token_expires_at")
  refreshTokenExpiresAt DateTime? @map("refresh_token_expires_at")
  scope                 String?
  idToken               String?   @map("id_token")
  password              String?
  updatedAt             DateTime  @updatedAt @map("updated_at")
  createdAt             DateTime  @default(now()) @map("created_at")
  user                  User      @relation(references: [id], fields: [userId], onDelete: Cascade)

  @@index([userId])
  @@map("accounts")
}

model Verification {
  id         String   @id @default(cuid())
  identifier String
  value      String
  expiresAt  DateTime @map("expires_at")
  updatedAt  DateTime @updatedAt @map("updated_at")
  createdAt  DateTime @default(now()) @map("created_at")

  @@index([identifier])
  @@map("verifications")
}

model Subscription {
  id              String    @id @default(cuid())
  userId          String    @map("user_id")
  subId           String    @unique @map("sub_id")
  customerId      String    @map("customer_id")
  productId       String    @map("product_id")
  variantId       String    @map("variant_id")
  status          String    @default("TRIALING")
  paymentProvider String    @map("payment_provider")
  endAt           DateTime? @map("end_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")
  createdAt       DateTime  @default(now()) @map("created_at")
  user            User      @relation(references: [id], fields: [userId])

  @@index([userId])
  @@index([customerId])
  @@map("subscriptions")
}

model Image {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  name      String   @unique
  url       String
  updatedAt DateTime @updatedAt @map("updated_at")
  createdAt DateTime @default(now()) @map("created_at")
  user      User     @relation(references: [id], fields: [userId], onDelete: Cascade)

  @@index([userId])
  @@map("images")
}

model Extra {
  id               String    @id @default(cuid())
  userId           String    @map("user_id")
  source           String    @map("source") // 来源：chrome / edge / safari / web
  browserAccountId String?   @map("browser_account_id")
  bookmarkId       String    @map("bookmark_id")
  bookmarkType     String?   @map("bookmark_type") // 类型：encrypted
  cover            String?
  description      String?
  note             String?
  emoji            String?
  iv               String?
  updatedAt        DateTime  @map("updated_at")
  createdAt        DateTime  @map("created_at")
  deletedAt        DateTime? @map("deleted_at")
  deleted          Boolean   @default(false)
  user             User      @relation(references: [id], fields: [userId], onDelete: Cascade)

  @@index([userId])
  @@index([browserAccountId])
  @@index([bookmarkId])
  @@map("extras")
}

model Tag {
  id               String    @id @default(cuid())
  userId           String    @map("user_id")
  source           String    @map("source") // 来源：chrome / edge / safari / web
  browserAccountId String?   @map("browser_account_id")
  name             String
  bookmarkIds      String    @map("bookmark_ids")
  color            String?
  updatedAt        DateTime  @map("updated_at")
  createdAt        DateTime  @map("created_at")
  deletedAt        DateTime? @map("deleted_at")
  deleted          Boolean   @default(false)
  user             User      @relation(references: [id], fields: [userId], onDelete: Cascade)

  @@index([userId])
  @@index([browserAccountId])
  @@index([name])
  @@map("tags")
}

model Shared {
  id               String       @id @default(cuid())
  slug             String       @unique
  userId           String       @map("user_id")
  source           String       @map("source") // 来源：chrome / edge / safari / web
  browserAccountId String?      @map("browser_account_id")
  bookmarkId       String?      @map("bookmark_id")
  bookmarkData     String       @map("bookmark_data")
  bookmarkDataHash String       @map("bookmark_data_hash")
  description      String?
  isPublic         Boolean      @default(false)
  inviteCode       String?
  viewsCount       Int          @default(0)
  updatedAt        DateTime     @map("updated_at")
  createdAt        DateTime     @map("created_at")
  deletedAt        DateTime?    @map("deleted_at")
  deleted          Boolean      @default(false)
  sharedUsers      SharedUser[]
  user             User         @relation(references: [id], fields: [userId], onDelete: Cascade)

  @@index([slug])
  @@index([userId])
  @@index([browserAccountId])
  @@index([inviteCode])
  @@map("shareds")
}

model SharedUser {
  id         String    @id @default(cuid())
  userId     String    @map("user_id")
  sharedId   String    @map("shared_id")
  role       String    @default("VIEWER")
  viewsCount Int       @default(0) @map("views_count")
  updatedAt  DateTime  @map("updated_at")
  createdAt  DateTime  @map("created_at")
  deletedAt  DateTime? @map("deleted_at")
  deleted    Boolean   @default(false)
  user       User      @relation(references: [id], fields: [userId], onDelete: Cascade)
  shared     Shared    @relation(references: [id], fields: [sharedId], onDelete: Cascade)

  @@index([userId])
  @@index([sharedId])
  @@map("shared_users")
}

model Snapshot {
  id               String   @id @default(cuid())
  userId           String   @map("user_id")
  source           String   @map("source") // 来源：chrome / edge / firefox / safari / web
  browserAccountId String?  @map("browser_account_id")
  dataHash         String   @map("data_hash")
  updatedAt        DateTime @updatedAt @map("updated_at")
  createdAt        DateTime @default(now()) @map("created_at")
  user             User     @relation(references: [id], fields: [userId], onDelete: Cascade)

  @@index([userId])
  @@index([browserAccountId])
  @@map("snapshots")
}
