import type { Send<PERSON><PERSON>Handler } from '../types'

export const sendEmail: SendEmailHandler = async ({ to, from, subject, html }) => {
  if (!process.env.PLUNK_API_KEY) {
    throw new Error('PLUNK_API_KEY is missing')
  }

  if (!to || !from || !html) {
    throw new Error('Required email fields are missing')
  }

  try {
    await $fetch('https://api.useplunk.com/v1/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.PLUNK_API_KEY}`,
      },
      body: JSON.stringify({
        to,
        from,
        subject,
        body: html,
      }),
    })
    console.log('Email sent successfully via Plunk')
  }
  catch (error) {
    console.error('Failed to send email with <PERSON>lunk:', error)
    throw error
  }
}
