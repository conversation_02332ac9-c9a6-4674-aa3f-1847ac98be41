export default defineEventHandler(async (event) => {
  // console.log('New request url: ' + getRequestURL(event))
  const url = event.node.req.url

  // Protect dashboard API routes
  if (url?.startsWith('/api/dashboard')) {
    // 如果当前用户未登录 requireUserSession 会自动抛出 401 错误
    await requireUserSession(event)
  }

  // Protect admin API routes
  if (url?.startsWith('/api/admin')) {
    const { user } = await requireUserSession(event)

    if (user.role !== 'ADMIN') {
      throw createError({
        statusCode: 403,
        statusMessage: 'Forbidden',
        message: 'Admin role required',
      })
    }
  }
})
