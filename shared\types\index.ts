// https://github.com/nuxt/nuxt/issues/28869
/* eslint-disable @typescript-eslint/no-empty-object-type */
import type {
  ComponentCustomOptions as _ComponentCustomOptions,
  ComponentCustomProperties as _ComponentCustomProperties,
} from 'vue'

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties extends _ComponentCustomProperties {}
  interface ComponentCustomOptions extends _ComponentCustomOptions {}
}
/* eslint-enable @typescript-eslint/no-empty-object-type */
