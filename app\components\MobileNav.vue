<script setup lang="ts">
const props = withDefaults(defineProps<{
  nav: any[]
  secondaryNav?: any[]
}>(), {
  nav: () => [],
  secondaryNav: () => [],
})

const open = ref(false)
</script>

<template>
  <Sheet v-model:open="open">
    <SheetTrigger as-child>
      <Icon
        name="radix-icons:hamburger-menu"
        class="size-6"
      />
    </SheetTrigger>
    <SheetContent
      side="left"
      class="pr-0"
    >
      <SheetTitle />
      <SheetDescription />
      <ScrollArea class="mt-[20px] h-[calc(100vh-45px)] pb-[40px]">
        <div class="flex flex-col space-y-3">
          <NuxtLink
            v-for="item in props.nav"
            :key="item.href"
            :href="item.href"
            @click="open = false"
          >
            {{ item.title }}
          </NuxtLink>
        </div>
        <div class="flex flex-col space-y-2">
          <div
            v-for="(items, index) in props.secondaryNav"
            :key="index"
            class="flex flex-col space-y-3 pt-6"
          >
            <div class="flex items-center">
              <h4 class="font-medium">
                {{ items.title }}
              </h4>
              <span
                v-if="items.label"
                class="ml-2 rounded-md bg-[#adfa1d] px-1.5 py-0.5 text-xs leading-none text-[#000000] no-underline group-hover:no-underline"
              >
                {{ items.label }}
              </span>
            </div>

            <NuxtLink
              v-for="item in items.items"
              :key="item.href"
              :href="item.href"
              class="inline-flex items-center text-muted-foreground"
              @click="open = false"
            >
              {{ item.title }}
              <span
                v-if="item.label"
                class="ml-2 rounded-md bg-[#adfa1d] px-1.5 py-0.5 text-xs leading-none text-[#000000] no-underline group-hover:no-underline"
              >
                {{ item.label }}
              </span>
            </NuxtLink>
          </div>
        </div>
      </ScrollArea>
    </SheetContent>
  </Sheet>
</template>
