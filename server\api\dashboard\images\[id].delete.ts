// import { delete } from "@@/server/libs/storage";

export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)
  const id = getRouterParam(event, 'id')

  try {
    const image = await db.image.delete({ where: { id, userId: user.id } })
    // Deletes a file from Amazon S3 storage.
    // await delete(image.name);
  }
  catch (error) {
    throw createError({
      statusCode: 404,
      message: 'Image not found or you don\'t have permission to delete it.',
    })
  }
})
