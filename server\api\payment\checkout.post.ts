import { createCustomerPortalLink, createCheckoutLink } from '@@/server/libs/payment'

import { z } from 'zod'

const checkoutLinkSchema = z.object({
  variantId: z.string(),
  redirectUrl: z.string().optional(),
})

export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)
  const { variantId, redirectUrl } = await readValidatedBody(event, body =>
    checkoutLinkSchema.parse(body),
  )

  const checkoutLink = await createCheckoutLink({
    userId: user.id,
    email: user.email,
    name: user.name,
    variantId,
    redirectUrl,
  })
  return checkoutLink
})
