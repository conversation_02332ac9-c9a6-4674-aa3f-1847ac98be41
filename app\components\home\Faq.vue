<script setup lang="ts">
const { contactEmail } = useRuntimeConfig().public

const faqs = [
  {
    question: 'What do I get exactly?',
    answer: 'The starter kit is a codebase that includes all the common functionality and tools you need to build a Saas / web app. It helps you to get started quickly and saves you a lot of time and headache when building a SaaS / Admin / Blog or any more application.',
  },
  {
    question: 'How is the codebase distributed?',
    answer: 'You will be added to the private Acme GitHub repositories. To receive an invite to the private repositories, please add your Github username to the checkout form after purchasing. Otherwise, you can send me an email and I\'ll do it manually. You will receive instructions to redeem an invite using our form.',
  },
  {
    question: 'Javascript or Typescript?',
    answer: 'Both! You can choose once you get access.',
  },
  {
    question: 'Do I need to be an expert to use Acme?',
    answer: 'No, you don\'t need to be an expert. With that said, you\'re expected to know how to write code, and be familiar with <PERSON>ue.',
  },
  {
    question: 'My tech stack is different, can I still use it?',
    answer: 'Yes, as long as you\'re comfortable with Vue. Libraries are independent. You can use SendGrid instead of Mailgun, LemonSqueezy instead of Stripe, or Postgres instead of MongoDB, for instance.',
  },
  {
    question: 'What am I allowed to do with the starter template?',
    answer: 'You are allowed to build unlimited projects with it (commercial projects too). You are not allowed to resell the code or parts of it. You are also not allowed to publish the code or parts of it as a template or boilerplate. See the license page for more details.',
  },
  {
    question: 'How many projects can I use it for?',
    answer: 'As many as you want.',
  },
  {
    question: 'I am a freelancer, and I want to use it for my clients. Can I?',
    answer: 'Yes, but you will need each client to purchase a license. You can purchase a license on their behalf, or you can send them a link to purchase it themselves.',
  },
  {
    question: 'Can I get a refund?',
    answer: 'After you\'ve got access to the repo, ShipFast is yours forever, so it can\'t be refunded. But rest assured, users of ShipFast ship startups in 7 days on average and make their first $ online in record time..',
  },
  {
    question: 'Are there any other costs associated?',
    answer: 'Many hosting platforms, like Vercel, let you host a project for free (front-end + back-end) and MongoDB/Supabase have free tiers — so you can launch for first app for $0/month.',
  },
  {
    question: 'What is your refund policy?',
    answer: 'If you are unhappy with your purchase, contact <NAME_EMAIL> within 15 days, and we\'ll give you a full refund.',
  },
]
</script>

<template>
  <Accordion
    type="single"
    class="w-full"
    collapsible
  >
    <AccordionItem
      v-for="(item, i) in faqs"
      :key="`faq-${i}`"
      :value="`faq-${i}`"
    >
      <AccordionTrigger class="text-left text-base">
        {{ item.question }}
      </AccordionTrigger>
      <AccordionContent class="text-base text-muted-foreground">
        {{ item.answer }}
      </AccordionContent>
    </AccordionItem>
  </Accordion>
  <p class="mt-8 text-center">
    Can&apos;t find the answer to your question?
    <NuxtLink
      target="_blank"
      :href="`mailto:${contactEmail}`"
      class="mx-1 font-semibold underline transition"
    >
      Contact
    </NuxtLink>
    to receive help from our team.
  </p>
</template>
