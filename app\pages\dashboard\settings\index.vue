<script setup lang="ts">
import { useFileDialog } from '@vueuse/core'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { z } from 'zod'
import { format as dateFormat } from 'date-fns'

definePageMeta({
  title: 'Dashboard Layout',
  layout: 'dashboard'
})

const { user } = useUserSession()

const userSchem = toTypedSchema(
  z.object({
    name: z.string().min(3),
    image: z.string().optional(),
    email: z.string().email('Invalid email'),
  }),
)

const form = useForm({
  validationSchema: userSchem,
})

const loading = ref(false)
const onSubmit = form.handleSubmit(async (values) => {
  try {
    loading.value = true
    await authClient.updateUser({
      name: values.name
    }, {
      onSuccess: (data) => {
        // 例如显示成功消息
        toast.success('用户信息更新成功')
      },
      onError: (error) => {
        toast.error(error.error.message)
      }
    })
    loading.value = false
  }
  catch (error) {
    console.error(error)
    toast.error(error.data?.message || 'An error occurred while updating the profile.')
  }
  finally {
    loading.value = false
  }
})

const fileUrl = ref(null)
const fileUploading = ref(false)
const { files, open: openFileDialog, onChange: onFileSelected } = useFileDialog({
  accept: 'image/*',
  multiple: false,
})

onFileSelected(async () => {
  const file = files.value[0]
  const formData = new FormData()
  formData.append('image', file)
  try {
    fileUploading.value = true
    const image = await $fetch('/api/images/upload', {
      method: 'POST',
      body: formData,
    })
    await $fetch('/api/account/update', {
      method: 'PATCH',
      body: {
        image: image,
      },
    })
    toast.success('Avatar updated successfully')
  }
  finally {
    fileUploading.value = false
  }
})

const { data: oauthAccounts, status: getOauthAccountStatus, refresh: refreshOauthAccount } = await useFetch('/api/account/oauth-accounts')
const unlinking = ref(null)
const getProviderIcon = (provider) => {
  const icons = {
    google: 'custom:ri-google-fill',
    github: 'ri:github-fill',
    twitter: 'ri:twitter-x-fill',
  }
  return icons[provider] || 'i-ph-link'
}
const unlinkOauthAccount = async (accountId) => {
  try {
    unlinking.value = accountId
    await $fetch('/api/account/oauth-accounts', {
      method: 'DELETE',
      body: { id: accountId },
    })
    toast.success('Account unlinked successfully')
    await refreshOauthAccount()
  }
  catch (error) {
    console.error(error)
    toast.error(error.data?.statusMessage ?? 'Failed to unlink account')
  }
  finally {
    unlinking.value = null
  }
}
</script>

<template>
  <section
    id="profile"
    class="mx-auto w-full max-w-full px-6 md:max-w-5xl"
  >
    <div class="flex justify-between py-8">
      <h1 class="flex items-center text-[28px] font-bold leading-[34px] tracking-[-0.416px]">
        Settings
      </h1>
    </div>

    <DashboardTabNavs />

    <div
      class="flex flex-col gap-6"
      style="opacity: 1; transform: none;"
    >
      <section class="rounded-lg border">
        <form
          id="userForm"
          @submit="onSubmit"
        >
          <div class="border-b px-6 py-4">
            <h2 class="font-bold">
              Profile
            </h2>
          </div>
          <div class="mb-2 flex max-w-md flex-col gap-4 p-4">
            <div class="flex items-center gap-4">
              <Avatar class="rounded-lg">
                <AvatarImage
                  :src="user.image || ''"
                  :alt="user.name"
                />
                <AvatarFallback class="rounded-lg">
                  {{ avatarName(user.name) }}
                </AvatarFallback>
              </Avatar>
              <Button
                variant="secondary"
                @click="openFileDialog"
              >
                <Icon
                  v-if="fileUploading"
                  name="svg-spinners:3-dots-fade"
                  class="mr-2"
                /> Change
              </Button>
            </div>
            <FormField
              v-slot="{ componentField }"
              name="email"
              :value="user.email"
            >
              <FormItem>
                <FormLabel
                  class="text-muted-foreground"
                  for="email"
                >
                  Email
                </FormLabel>
                <FormControl>
                  <Input
                    v-bind="componentField"
                    disabled
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
            <FormField
              v-slot="{ componentField }"
              name="name"
              :value="user.name"
            >
              <FormItem>
                <FormLabel
                  class="text-muted-foreground"
                  for="name"
                >
                  name
                </FormLabel>
                <FormControl>
                  <Input
                    type="text"
                    v-bind="componentField"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          </div>
          <div class="flex justify-between border-t py-3 pl-5 pr-3">
            <Button>Update</Button>
          </div>
        </form>
      </section>

      <section class="rounded-lg border">
        <div class="border-b px-6 py-4">
          <h2 class="font-bold">
            Oauth Account
          </h2>
        </div>
        <div class="mb-2 flex flex-col gap-4 p-4">
          <p
            v-if="oauthAccounts && oauthAccounts.length === 0"
            class="text-center text-muted-foreground"
          >
            You don't have any linked accounts.
          </p>
          <ul
            v-else
            role="list"
            class="divide-y"
          >
            <li
              v-for="oauthAccount in oauthAccounts"
              :key="oauthAccount.id"
              class="flex items-center justify-between gap-6 py-4"
            >
              <div class="flex gap-x-4">
                <Icon
                  :name="getProviderIcon(oauthAccount.provider)"
                  class="size-6 flex-none rounded-full"
                />
                <div class="flex min-w-0 flex-auto items-center gap-x-2">
                  <p class="text-sm font-semibold capitalize leading-6">
                    {{ oauthAccount.provider }}
                  </p>
                  <p class="mt-px truncate text-xs leading-5 text-muted-foreground">
                    Connected on {{ dateFormat(oauthAccount.createdAt, "dd MMMM yyyy") }}
                  </p>
                </div>
              </div>
              <Button
                variant="destructive"
                size="sm"
                :disabled="unlinking === oauthAccount.id"
                @click="unlinkOauthAccount(oauthAccount.id)"
              >
                <Icon
                  v-if="unlinking === oauthAccount.id"
                  name="svg-spinners:3-dots-fade"
                  class="mr-2"
                /> Unlink
              </Button>
            </li>
          </ul>
        </div>
      </section>
    </div>
  </section>
</template>
