<script setup lang="ts">
import { format as dateFormat } from 'date-fns'
import ListItem from './components/ListItem.vue'
import GridItem from './components/GridItem.vue'

const isList = ref(false)

const searchValue = ref('')
const debouncedSearch = refDebounced(searchValue, 250)

const { data: blogs } = await useAsyncData('blogs', () => queryContent('/blogs')
  .where({
    $and: [
      { draft: { $not: true } },
    ],
  })
  .sort({ date: -1 })
  .find())

const filteredBlogs = computed(() => {
  let output = blogs.value

  const searchValue = debouncedSearch.value?.trim().toLowerCase()

  if (searchValue) {
    output = blogs.value.filter((item) => {
      return item.title.toLowerCase().includes(searchValue)
        || item.description.toLowerCase().includes(searchValue)
        || item.author?.name.toLowerCase().includes(searchValue)
    })
  }

  return output
})

useSeoMeta({
  title: 'Blogs',
})
</script>

<template>
  <div class="container">
    <div class="mx-auto mt-6 lg:mt-8">
      <h2 class="font-gradient mt-20 text-center text-5xl tracking-tight md:text-6xl">
        Blog
      </h2>
      <div class="mt-20 flex flex-row items-center justify-between gap-2">
        <div
          class="flex h-auto w-full items-stretch justify-end gap-2 lg:max-w-[240px] xl:max-w-[280px]"
          style="opacity: 1;"
        >
          <div class="grid w-full gap-2 text-sm leading-4 md:grid md:grid-cols-12">
            <div class="relative col-span-12">
              <Input
                id="search"
                v-model="searchValue"
                type="text"
                placeholder="Search"
                class="pl-10"
              />
              <span class="absolute inset-y-0 start-0 flex items-center justify-center px-4">
                <Icon
                  name="lucide:search"
                  class="text-muted-foreground"
                />
              </span>
            </div>
          </div>
        </div>
        <Button
          variant="outline"
          size="icon"
          class="bg-muted/60"
          @click="isList = !isList"
        >
          <Icon :name="isList ? 'lucide:grid' : 'lucide:list'" />
        </Button>
      </div>

      <ol
        v-if="filteredBlogs?.length"
        :class="cn(
          'grid -mx-2 sm:-mx-4 py-6 lg:py-6 lg:pb-20',
          isList ? 'grid-cols-1' : 'grid-cols-12 lg:gap-4',
        )"
      >
        <div
          v-for="(blog, idx) in filteredBlogs"
          :key="idx"
          :class="cn(
            isList ? 'col-span-12 px-2 sm:px-4 [&_a]:last:border-none' : 'col-span-12 mb-4 md:col-span-12 lg:col-span-6 xl:col-span-4 h-full',
          )"
        >
          <ListItem
            v-if="isList"
            :blog="blog"
          />
          <GridItem
            v-else
            :blog="blog"
            :idx="idx"
          />
        </div>
      </ol>
      <p
        v-else
        class="col-span-full mt-16 text-center"
      >
        No results.
      </p>
    </div>
  </div>
</template>
