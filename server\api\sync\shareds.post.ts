import { z } from 'zod'
import { useSSE } from '~/composables/useSSE'
// used in the pull.stream$ below

export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)
  const { sendToUser } = useSSE()
  let lastEventId = 0;

  const { changeRows } = await readBody(event)

  const conflicts = [];
  const data = {
    id: lastEventId++,
    documents: [] as any[],
    checkpoint: null as any
  };
  for(const changeRow of changeRows){
    const realMasterState = await db.shared.findUnique({
      where: {
        id: changeRow.newDocumentState.id
      }
    })
    // realMasterState 是数据库中的最新状态。
    // assumedMasterState 是客户端在修改数据时，假设的服务端当前状态。换句话说，它是客户端在本地修改数据之前，从服务端获取的文档的最新状态。
    // newDocumentState 是客户端希望将文档更新为的新状态。它是客户端对文档的修改结果。
    // 冲突仅在以下两种情况下发生(数据库中已存在相同 ID 数据)： 
    // 1. realMasterState 存在但 assumedMasterState 不存在：客户端在修改数据时，假设的服务端当前状态不存在，说明客户端没有从服务端获取到文档的最新状态，因此无法判断是否存在冲突。
    // 2. realMasterState 存在且 assumedMasterState 存在，但 assumedMasterState 的 updatedAt 和 realMasterState 的 updatedAt 不一致：客户端在修改数据时，假设的服务端当前状态存在，但和数据库中的对应数据的 updatedAt 不一致，说明客户端在本地修改数据时，服务端已经更新了文档，因此存在冲突。
    if(
        realMasterState && !changeRow.assumedMasterState ||
        (
          realMasterState && changeRow.assumedMasterState &&
           // * For simplicity we detect conflicts on the server by only compare the updatedAt value.
           // * In reality you might want to do a more complex check or do a deep-equal comparison.
           new Date(realMasterState.updatedAt).getTime() !== new Date(changeRow.assumedMasterState.updatedAt).getTime()
        )
    ) {
        // we have a conflict / 将时间转为整数给客户端 rxdb
        // console.warn('--------- conflict --------', 
        //   typeof realMasterState.updatedAt, realMasterState.updatedAt, 
        //   typeof changeRow.assumedMasterState.updatedAt, changeRow.assumedMasterState.updatedAt,
        //   realMasterState.updatedAt !== changeRow.assumedMasterState.updatedAt,
        //   realMasterState.updatedAt != changeRow.assumedMasterState.updatedAt,
        //   new Date(realMasterState.updatedAt).getTime() !== new Date(changeRow.assumedMasterState.updatedAt).getTime()
        // );
        conflicts.push({
          ...realMasterState,
        });
    } else {
        // no conflict -> write the document
        console.warn('--------- no conflict -> write the document --------')
        await db.shared.upsert({
          where: {
            id: changeRow.newDocumentState.id,
          },
          update: {
            ...changeRow.newDocumentState,
          },
          create: {
            ...changeRow.newDocumentState,
          },
        })
        
        data.documents.push(changeRow.newDocumentState);
        data.checkpoint = { updatedAt: changeRow.newDocumentState.updatedAt };
    }
  }

  // 通知所有客户端，有数据更新 - 提交修改的客户端也会收到，并且会重新拉取一次，这是否合理？
  if(data.documents.length > 0){
    sendToUser(user.id, 'RESYNC')
  }

  // 当检查点迭代到达最后一个检查点时，由于没有更新的文档，后端返回一个空数组，复制将自动切换到事件观察模式
  return conflicts
})
