import { z } from 'zod';
import type { Prisma } from '@prisma/client';

/////////////////////////////////////////
// HELPER FUNCTIONS
/////////////////////////////////////////


/////////////////////////////////////////
// ENUMS
/////////////////////////////////////////

export const TransactionIsolationLevelSchema = z.enum(['Serializable']);

export const UserScalarFieldEnumSchema = z.enum(['id','name','email','emailVerified','image','active','role','plan','updatedAt','createdAt','encryptSalt','encryptKey','encryptIv','encryptAlgo']);

export const SessionScalarFieldEnumSchema = z.enum(['id','userId','token','expiresAt','ipAddress','userAgent','updatedAt','createdAt']);

export const AccountScalarFieldEnumSchema = z.enum(['id','userId','accountId','providerId','accessToken','refreshToken','accessTokenExpiresAt','refreshTokenExpiresAt','scope','idToken','password','updatedAt','createdAt']);

export const VerificationScalarFieldEnumSchema = z.enum(['id','identifier','value','expiresAt','updatedAt','createdAt']);

export const SubscriptionScalarFieldEnumSchema = z.enum(['id','userId','subId','customerId','productId','variantId','status','paymentProvider','endAt','updatedAt','createdAt']);

export const ImageScalarFieldEnumSchema = z.enum(['id','userId','name','url','updatedAt','createdAt']);

export const ExtraScalarFieldEnumSchema = z.enum(['id','userId','source','browserAccountId','bookmarkId','bookmarkType','cover','description','note','emoji','iv','updatedAt','createdAt','deletedAt','deleted']);

export const TagScalarFieldEnumSchema = z.enum(['id','userId','source','browserAccountId','name','bookmarkIds','color','updatedAt','createdAt','deletedAt','deleted']);

export const SharedScalarFieldEnumSchema = z.enum(['id','slug','userId','source','browserAccountId','bookmarkId','bookmarkData','bookmarkDataHash','description','isPublic','inviteCode','viewsCount','updatedAt','createdAt','deletedAt','deleted']);

export const SharedUserScalarFieldEnumSchema = z.enum(['id','userId','sharedId','role','viewsCount','updatedAt','createdAt','deletedAt','deleted']);

export const SnapshotScalarFieldEnumSchema = z.enum(['id','userId','source','browserAccountId','dataHash','updatedAt','createdAt']);

export const SortOrderSchema = z.enum(['asc','desc']);

export const NullsOrderSchema = z.enum(['first','last']);
/////////////////////////////////////////
// MODELS
/////////////////////////////////////////

/////////////////////////////////////////
// USER SCHEMA
/////////////////////////////////////////

export const UserSchema = z.object({
  id: z.string().cuid(),
  name: z.string(),
  email: z.string(),
  emailVerified: z.boolean(),
  image: z.string().nullable(),
  active: z.boolean(),
  role: z.string(),
  plan: z.string(),
  updatedAt: z.coerce.date(),
  createdAt: z.coerce.date(),
  encryptSalt: z.string().nullable(),
  encryptKey: z.string().nullable(),
  encryptIv: z.string().nullable(),
  encryptAlgo: z.string().nullable(),
})

export type User = z.infer<typeof UserSchema>

/////////////////////////////////////////
// SESSION SCHEMA
/////////////////////////////////////////

export const SessionSchema = z.object({
  id: z.string().cuid(),
  userId: z.string(),
  token: z.string(),
  expiresAt: z.coerce.date(),
  ipAddress: z.string().nullable(),
  userAgent: z.string().nullable(),
  updatedAt: z.coerce.date(),
  createdAt: z.coerce.date(),
})

export type Session = z.infer<typeof SessionSchema>

/////////////////////////////////////////
// ACCOUNT SCHEMA
/////////////////////////////////////////

export const AccountSchema = z.object({
  id: z.string().cuid(),
  userId: z.string(),
  accountId: z.string(),
  providerId: z.string(),
  accessToken: z.string().nullable(),
  refreshToken: z.string().nullable(),
  accessTokenExpiresAt: z.coerce.date().nullable(),
  refreshTokenExpiresAt: z.coerce.date().nullable(),
  scope: z.string().nullable(),
  idToken: z.string().nullable(),
  password: z.string().nullable(),
  updatedAt: z.coerce.date(),
  createdAt: z.coerce.date(),
})

export type Account = z.infer<typeof AccountSchema>

/////////////////////////////////////////
// VERIFICATION SCHEMA
/////////////////////////////////////////

export const VerificationSchema = z.object({
  id: z.string().cuid(),
  identifier: z.string(),
  value: z.string(),
  expiresAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  createdAt: z.coerce.date(),
})

export type Verification = z.infer<typeof VerificationSchema>

/////////////////////////////////////////
// SUBSCRIPTION SCHEMA
/////////////////////////////////////////

export const SubscriptionSchema = z.object({
  id: z.string().cuid(),
  userId: z.string(),
  subId: z.string(),
  customerId: z.string(),
  productId: z.string(),
  variantId: z.string(),
  status: z.string(),
  paymentProvider: z.string(),
  endAt: z.coerce.date().nullable(),
  updatedAt: z.coerce.date(),
  createdAt: z.coerce.date(),
})

export type Subscription = z.infer<typeof SubscriptionSchema>

/////////////////////////////////////////
// IMAGE SCHEMA
/////////////////////////////////////////

export const ImageSchema = z.object({
  id: z.string().cuid(),
  userId: z.string(),
  name: z.string(),
  url: z.string(),
  updatedAt: z.coerce.date(),
  createdAt: z.coerce.date(),
})

export type Image = z.infer<typeof ImageSchema>

/////////////////////////////////////////
// EXTRA SCHEMA
/////////////////////////////////////////

export const ExtraSchema = z.object({
  id: z.string().cuid(),
  userId: z.string(),
  source: z.string(),
  browserAccountId: z.string().nullable(),
  bookmarkId: z.string(),
  bookmarkType: z.string().nullable(),
  cover: z.string().nullable(),
  description: z.string().nullable(),
  note: z.string().nullable(),
  emoji: z.string().nullable(),
  iv: z.string().nullable(),
  updatedAt: z.coerce.date(),
  createdAt: z.coerce.date(),
  deletedAt: z.coerce.date().nullable(),
  deleted: z.boolean(),
})

export type Extra = z.infer<typeof ExtraSchema>

/////////////////////////////////////////
// TAG SCHEMA
/////////////////////////////////////////

export const TagSchema = z.object({
  id: z.string().cuid(),
  userId: z.string(),
  source: z.string(),
  browserAccountId: z.string().nullable(),
  name: z.string(),
  bookmarkIds: z.string(),
  color: z.string().nullable(),
  updatedAt: z.coerce.date(),
  createdAt: z.coerce.date(),
  deletedAt: z.coerce.date().nullable(),
  deleted: z.boolean(),
})

export type Tag = z.infer<typeof TagSchema>

/////////////////////////////////////////
// SHARED SCHEMA
/////////////////////////////////////////

export const SharedSchema = z.object({
  id: z.string().cuid(),
  slug: z.string(),
  userId: z.string(),
  source: z.string(),
  browserAccountId: z.string().nullable(),
  bookmarkId: z.string().nullable(),
  bookmarkData: z.string(),
  bookmarkDataHash: z.string(),
  description: z.string().nullable(),
  isPublic: z.boolean(),
  inviteCode: z.string().nullable(),
  viewsCount: z.number().int(),
  updatedAt: z.coerce.date(),
  createdAt: z.coerce.date(),
  deletedAt: z.coerce.date().nullable(),
  deleted: z.boolean(),
})

export type Shared = z.infer<typeof SharedSchema>

/////////////////////////////////////////
// SHARED USER SCHEMA
/////////////////////////////////////////

export const SharedUserSchema = z.object({
  id: z.string().cuid(),
  userId: z.string(),
  sharedId: z.string(),
  role: z.string(),
  viewsCount: z.number().int(),
  updatedAt: z.coerce.date(),
  createdAt: z.coerce.date(),
  deletedAt: z.coerce.date().nullable(),
  deleted: z.boolean(),
})

export type SharedUser = z.infer<typeof SharedUserSchema>

/////////////////////////////////////////
// SNAPSHOT SCHEMA
/////////////////////////////////////////

export const SnapshotSchema = z.object({
  id: z.string().cuid(),
  userId: z.string(),
  source: z.string(),
  browserAccountId: z.string().nullable(),
  dataHash: z.string(),
  updatedAt: z.coerce.date(),
  createdAt: z.coerce.date(),
})

export type Snapshot = z.infer<typeof SnapshotSchema>
