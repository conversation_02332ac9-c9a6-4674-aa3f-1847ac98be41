<script setup lang="ts">
defineProps({
  name: {
    type: String,
    required: true,
  },
})
</script>

<template>
  <template v-if="name === 'ball-light'">
    <div
      aria-hidden="true"
      class="pointer-events-none absolute left-1/2 top-0 h-px w-[300px] max-w-full -translate-x-1/2 -translate-y-1/2"
      style="background: linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, rgba(255, 255, 255, 0) 0%, rgba(143, 143, 143, 0.67) 50%, rgba(0, 0, 0, 0) 100%);"
    />
    <div
      aria-hidden="true"
      class="pointer-events-none absolute -top-1 left-1/2 h-[200px] w-full max-w-full -translate-x-1/2 -translate-y-1/2 md:max-w-[400px]"
      style="background:radial-gradient(rgba(200, 200, 200, 0.1) 0%, transparent 80%);"
    />
  </template>
</template>
