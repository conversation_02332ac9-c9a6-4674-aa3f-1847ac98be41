<script setup lang="ts">
import type { Row } from '@tanstack/vue-table'
import { computed } from 'vue'

interface DataTableRowActionsProps {
  row: Row<any>
}
const props = defineProps<DataTableRowActionsProps>()

const emit = defineEmits<{
  show: [task: object | null]
  openEdit: [task: object | null]
  remove: [task: object | null]
}>()

const task = computed(() => props.row.original)

const deleteTask = async () => {
  try {
    await $fetch(`/api/tasks/${task.value.id}`, {
      method: 'DELETE',
    })
    emit('remove', task)
    toast.success('Task deleted')
  }
  catch (error) {
    toast.error('Failed to delete task')
  }
}
</script>

<template>
  <div class="flex justify-end">
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <Button
          variant="ghost"
          class="flex size-8 p-0 data-[state=open]:bg-muted"
        >
          <Icon name="radix-icons:dots-horizontal" class="size-4" />
          <span class="sr-only">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        class="w-[160px]"
      >
        <DropdownMenuItem @click="$emit('show', task)">
          Show
        </DropdownMenuItem>
        <DropdownMenuItem @click="emit('openEdit', task)">
          Edit
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem @click="deleteTask">
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
</template>
