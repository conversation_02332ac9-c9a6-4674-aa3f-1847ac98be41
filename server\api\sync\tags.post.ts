import { Tag } from '@prisma/client';
import { z } from 'zod'
import { useSSE } from '~/composables/useSSE'
// used in the pull.stream$ below

export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)
  const { sendToUser } = useSSE()

  let lastEventId = 0;
  const { changeRows } = await readBody(event)

  console.warn('------------- get push -------------', changeRows)

  const conflicts = [];
  const data = {
    id: lastEventId++,
    documents: [] as Tag[],
    checkpoint: null as any
  };
  for(const changeRow of changeRows){
    const realMasterState = await db.tag.findUnique({
      where: {
        id: changeRow.newDocumentState.id
      }
    })

    // 冲突判断逻辑： 1. 数据库中已存在相同 ID 数据，2，assumedMasterState 不存在或者和数据库中的对应数据的 updatedAt 不一致
    if(
        realMasterState && !changeRow.assumedMasterState ||
        (
          realMasterState && changeRow.assumedMasterState &&
           // * For simplicity we detect conflicts on the server by only compare the updatedAt value.
           // * In reality you might want to do a more complex check or do a deep-equal comparison.
          realMasterState.updatedAt !== changeRow.assumedMasterState.updatedAt
        )
    ) {
        console.warn('--------- we have a conflict --------', realMasterState )
        // we have a conflict / 将时间转为整数给客户端 rxdb
        conflicts.push({
          ...realMasterState,
        });
    } else {
        // no conflict -> write the document
        console.warn('--------- no conflict -> write the document --------')
        await db.tag.upsert({
          where: {
            id: changeRow.newDocumentState.id,
          },
          update: {
            ...changeRow.newDocumentState,
          },
          create: {
            ...changeRow.newDocumentState,
          },
        })
        
        data.documents.push(changeRow.newDocumentState);
        data.checkpoint = { updatedAt: changeRow.newDocumentState.updatedAt };
    }
  }
  if(data.documents.length > 0){
    sendToUser(user.id, 'RESYNC')
  }

  // 当检查点迭代到达最后一个检查点时，由于没有更新的文档，后端返回一个空数组，复制将自动切换到事件观察模式
  return conflicts
})
