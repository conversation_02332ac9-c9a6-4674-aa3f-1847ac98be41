<script setup lang="ts">
const blocks = [{
  title: 'Efficient Design',
  description: 'A streamlined approach to web development that simplifies the process and reduces the time to market, allowing for quicker deployment of your application.',
  tag: { name: 'Nuxt', class: 'bg-green-500/20 text-green-500' },
},
{
  title: 'Modular System',
  description: 'Easily integrate components and plugins to create a tailor-made solution that fits your project\'s unique requirements, enhancing flexibility and maintainability.',
  tag: { name: 'Modular', class: 'bg-green-500/20 text-green-500' },
},
{
  title: 'SEO Friendly',
  description: 'Optimized for search engines right out of the box, ensuring that your content is easily discoverable by your target audience and improving your online visibility.',
  tag: { name: 'SEO', class: 'bg-blue-500/20 text-blue-500' },
},
{
  title: 'Performance Boost',
  description: 'Leverage the power of Vue.js and Nuxt.js to achieve faster page loads and a smoother user experience, which is crucial for retaining users and improving conversion rates.',
  tag: { name: 'Speed', class: 'bg-green-500/20 text-green-500' },
},
{
  title: 'Scalability',
  description: 'Build large applications with ease, as Nuxt.js is designed to handle complex projects and scale efficiently as your user base grows, ensuring stability and performance.',
  tag: { name: 'Scalable', class: 'bg-green-500/20 text-green-500' },
},
{
  title: 'Cross-Platform',
  description: 'Works seamlessly across all devices and platforms, ensuring that your application is accessible to a wide range of users and providing a consistent experience.',
  tag: { name: 'Cross-Platform', class: 'bg-rose-500/20 text-rose-500' },
},
{
  title: 'Real-Time Updates',
  description: 'Keep your users informed with real-time data updates, ensuring that they always have the most current information at their fingertips, which is essential for dynamic applications.',
  tag: { name: 'Real-Time', class: 'bg-orange-500/20 text-orange-500' },
},
{
  title: 'Customizable Themes',
  description: 'Easily switch between themes or create your own to match your brand\'s identity, giving your application a unique and cohesive look that resonates with your audience.',
  tag: { name: 'Themes', class: 'bg-green-500/20 text-green-500' },
},
{
  title: 'Community Support',
  description: 'A vibrant community of developers and users is available to help you with any issues, providing a wealth of knowledge and support that can be invaluable for troubleshooting and learning.',
  tag: { name: 'Support', class: 'bg-green-500/20 text-green-500' },
},
]
</script>

<template>
  <div class="relative mx-auto mt-24 grid w-full max-w-5xl grid-cols-1 sm:grid-cols-2 md:flex-row lg:grid-cols-3">
    <div
      v-for="(block, index) in blocks"
      :key="index"
      class="grid-list flex w-full flex-col gap-3 text-pretty p-4 md:gap-2 md:px-10 md:pb-12 md:pt-9 lg:border-b lg:border-r"
    >
      <h4 class="bg-slate-50 text-xl leading-[130%] md:leading-none">
        {{ block.title }}
      </h4>
      <p class="mt-1 text-sm leading-[1.6] text-muted-foreground">
        {{ block.description }}
      </p>
      <div class="inline">
        <span
          class="inline-flex h-7 select-none items-center whitespace-nowrap rounded-md px-2 text-xs font-medium"
          :class="block.tag.class"
        >{{ block.tag.name }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
  .grid-list:nth-child(3n) {
    border-right: none;
  }

  .grid-list:nth-child(7),
  .grid-list:nth-child(8),
  .grid-list:nth-child(9) {
    border-bottom: none;
  }
</style>
