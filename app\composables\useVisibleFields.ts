import { ref } from 'vue'

export type VisibleFieldsType = {
  id: boolean
  url: boolean
  tags: boolean
  note: boolean
  emoji: boolean
}

// 默认所有字段都可见
const defaultVisibleFields: VisibleFieldsType = {
  id: false,
  url: false,
  tags: false,
  note: true,
  emoji: true
}

// 从 defaultVisibleFields 中获取字段列表
const fieldList = Object.keys(defaultVisibleFields) as Array<keyof VisibleFieldsType>

// 创建一个全局状态
const visibleFields = ref<VisibleFieldsType>({...defaultVisibleFields})

export function useVisibleFields() {
  // 从存储加载配置
  async function loadVisibleFields() {
    try {
      const storedFields = localStorage.getItem('listVisibleFields')
      if (storedFields) {
        visibleFields.value = {
          ...defaultVisibleFields,
          ...JSON.parse(storedFields)
        }
      }
    } catch (error) {
      console.error('加载显示配置失败:', error)
    }
  }

  // 更新字段可见性
  const updateVisibleField = (field: keyof VisibleFieldsType, value: boolean) => {
    const newFields = { ...visibleFields.value, [field]: value }
    visibleFields.value = newFields
    localStorage.setItem('listVisibleFields', JSON.stringify(newFields))
  }

  return {
    visibleFields,
    fieldList,
    loadVisibleFields,
    updateVisibleField
  }
} 