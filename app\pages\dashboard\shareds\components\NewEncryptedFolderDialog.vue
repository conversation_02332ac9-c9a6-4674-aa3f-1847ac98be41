<script setup lang="ts">
import { z } from 'zod'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { useCrypto } from '@/composables/useCrypto'

const props = defineProps<{
  open: boolean
}>()

const emit = defineEmits<{
  openChange: [open: boolean]
  saved: [bookmark: object | null]
}>()

const bookmarkSchema = toTypedSchema(
  z.object({
    title: z.string().min(2),
    category: z.string().min(2).default('encrypted'),
    parentId: z.string().optional(),
  }),
)
const form = useForm({
  validationSchema: bookmarkSchema,
})

const { encryptBookmark } = useCrypto()

const loading = ref(false)
const onSubmit = form.handleSubmit(async (values) => {
  try {
    loading.value = true
    
    // 加密书签数据
    const encryptedData = await encryptBookmark(values)
    
    if (!encryptedData) {
      loading.value = false
      return // 如果加密失败，中止提交
    }
    
    // 发送加密后的数据到服务器
    const updatedBookmark = await $fetch(`/api/bookmarks`, { 
      method: 'POST', 
      body: encryptedData 
    })
    
    emit('saved', updatedBookmark)
    emit('openChange', false)
    toast('Encrypted bookmark created')
  }
  catch (error) {
    console.warn('Error: ', error)
    loading.value = false
    toast.error(error.data ? error.data.statusMessage : 'An unknown error occurred')
  }
})
</script>

<template>
  <Dialog
    :open="open"
    @update:open="(value: boolean) => emit('openChange', value)"
  >
    <DialogContent class="sm:max-w-[425px]">
      <DialogHeader>
        <DialogTitle>New Encrypted Bookmark</DialogTitle>
        <DialogDescription>
          Make new encrypted bookmark here. Click save when you're done.
        </DialogDescription>
      </DialogHeader>

      <form
        id="dialogForm"
        class="flex flex-col items-stretch gap-4"
        :validation-schema="bookmarkSchema"
        @submit="onSubmit"
      >
        <FormField
          v-slot="{ componentField }"
          name="title"
        >
          <FormItem>
            <FormControl>
              <Input
                type="text"
                placeholder="title"
                v-bind="componentField"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
      </form>

      <DialogFooter>
        <Button
          type="submit"
          form="dialogForm"
        >
          Create
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
