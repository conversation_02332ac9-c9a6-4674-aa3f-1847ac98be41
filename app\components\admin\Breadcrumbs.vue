<script setup lang="ts">
import type { RouteRecordRaw } from '#vue-router'

const route = useRoute()
const router = useRouter()
const getBreadcrumbs = () => {
  const fullPath = route.path
  const requestPath = fullPath.startsWith('/')
    ? fullPath.substring(1)
    : fullPath
  const crumbs = requestPath.split('/')
  const breadcrumbs: Array<RouteRecordRaw> = []
  let path = ''
  crumbs.forEach((crumb) => {
    if (crumb) {
      path = `${path}/${crumb}`
      const breadcrumb = router.getRoutes().find(r => r.path === path)
      if (breadcrumb) {
        breadcrumbs.push(breadcrumb)
      }
    }
  })
  return breadcrumbs
}
const ariaCurrent = (index: number): string =>
  index === getBreadcrumbs().length - 1 ? 'page' : 'false'
</script>

<template>
  <Breadcrumb>
    <BreadcrumbList>
      <template
        v-for="(breadcrumb, index) in getBreadcrumbs()"
        :key="index"
      >
        <BreadcrumbItem>
          <NuxtLink
            v-if="ariaCurrent(index) !== 'page'"
            :to="breadcrumb.path"
            :aria-current="ariaCurrent(index)"
          >
            {{ breadcrumb.meta?.title }}
          </NuxtLink>
          <BreadcrumbPage v-else>
            {{ breadcrumb.meta?.title }}
          </BreadcrumbPage>
        </BreadcrumbItem>
        <BreadcrumbSeparator v-if="ariaCurrent(index) !== 'page'" />
      </template>
    </BreadcrumbList>
  </Breadcrumb>
</template>
