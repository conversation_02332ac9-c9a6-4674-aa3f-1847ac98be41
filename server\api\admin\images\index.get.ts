export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const page = Number(query.page || 1)
  const pageSize = Number(query.pageSize || 10)
  const search = String(query.search || '')
  const filter = String(query.filter || 'all')
  const orderBy = JSON.parse(String(query.orderBy || '{"createdAt":"desc"}'))

  const searchConditions = query.search
    ? {
        OR: [
          {
            name: {
              contains: String(query.search),
            },
          },
        ],
      }
    : null

  const where = {
    ...searchConditions,
  }

  const [images, total] = await db.$transaction([
    db.image.findMany({
      where,
      skip: (page - 1) * pageSize || 0,
      take: pageSize || 20,
      orderBy,
    }),
    db.image.count({
      where,
    }),
  ])

  return { images, total }
})
