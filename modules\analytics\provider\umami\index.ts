// If you need to enable this feature, please uncomment this code and install the required package.

/*
import { useHead } from '#imports'

export const useAnalytics = () => {
  const init = () => {
    if (!import.meta.client || !import.meta.env.VITE_UMAMI_TRACKING_ID) {
      return
    }

    const umamiTrackingId = import.meta.env.VITE_UMAMI_TRACKING_ID

    useHead({
      script: [
        {
          'key': 'analytics-umami',
          'src': 'https://analytics.eu.umami.is/script.js',
          'async': true,
          'data-website-id': umamiTrackingId,
        },
      ],
    })
  }

  const trackEvent = (event: string, data?: Record<string, any>) => {
    if (typeof window === 'undefined' || !(window as any).umami) {
      return
    }

    (window as any).umami.track(event, {
      props: data,
    })
  }

  return {
    init,
    trackEvent,
  }
}
*/
