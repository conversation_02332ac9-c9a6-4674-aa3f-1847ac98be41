import { auth } from '@@/auth'

/**
 * 基于 better-auth 实现的 requireUserSession 函数
 * 兼容原有的 nuxt-auth-utils 的 requireUserSession API
 * 
 * @param event - Nuxt event handler 事件对象
 * @returns Promise<{ user: User, session: Session }>
 * @throws 401 错误如果用户未认证
 */
export async function requireUserSession(event: any) {
  const session = await auth.api.getSession({
    headers: event.headers
  })

  if (!session?.user) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
      message: 'Authentication required'
    })
  }

  return {
    user: session.user,
    session: session
  }
}
