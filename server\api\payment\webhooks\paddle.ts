import type {
  EventEntity,
  SubscriptionCreatedEvent } from '@paddle/paddle-node-sdk'
import {
  CustomerCreatedEvent,
  CustomerUpdatedEvent,
  EventName,
  SubscriptionUpdatedEvent,
} from '@paddle/paddle-node-sdk'
import { SubscriptionStatus } from '@@/server/libs/payment/types'
import { getPaddleInstance } from '@@/server/libs/payment/provider/paddle'

interface CustomData {
  user_id: string
}

export default defineEventHandler(async (event) => {
  try {
    const paddleInstance = getPaddleInstance()

    const webhookSecret = process.env.PADDLE_NOTIFICATION_WEBHOOK_SECRET
    if (!webhookSecret) {
      throw new Error('Paddle Webhook Secret is missing')
    }

    const body = await readRawBody(event)
    const paddleSignature = getHeader(event, 'paddle-signature')
    if (!paddleSignature) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Paddle signature is missing',
      })
    }

    if (!body) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Request body is missing',
      })
    }

    let paddleEvent: EventEntity | null
    try {
      paddleEvent = await paddleInstance.webhooks.unmarshal(body, webhookSecret, paddleSignature)
      // console.warn('----------------- paddleEvent --------------', paddleEvent)
    }
    catch (err) {
      throw createError({
        statusCode: 400,
        statusMessage: `Webhook Error: ${err}`,
      })
    }

    const relevantEvents = [
      'subscription.created',
      'subscription.updated',
      'subscription.canceled',
      'transaction.completed'
    ]

    if (!paddleEvent) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid event data',
      })
    }

    if (!relevantEvents.includes(paddleEvent.eventType)) {
      console.log('Not a relevant event')
      return 'OK'
    }

    const statusMap = {
      active: SubscriptionStatus.ACTIVE,
      past_due: SubscriptionStatus.PAST_DUE,
      unpaid: SubscriptionStatus.UNPAID,
      canceled: SubscriptionStatus.CANCELED,
      incomplete: SubscriptionStatus.INCOMPLETE,
      incomplete_expired: SubscriptionStatus.EXPIRED,
      trialing: SubscriptionStatus.TRIALING,
      paused: SubscriptionStatus.PAUSED,
      completed: SubscriptionStatus.ACTIVE,
    }

    const data = paddleEvent.data as SubscriptionCreatedEvent['data'] & { customData: { user_id: string } }

    if (!data.items?.[0]) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid subscription data',
      })
    }

    const subscriptionPayload = {
      userId: data.customData?.user_id,
      subId: String(data.id),
      customerId: String(data.customerId),
      productId: String(data.items[0].product?.id ?? ''),
      variantId: String(data.items[0].price?.id ?? ''),
      status: statusMap[data.status],
      paymentProvider: 'paddle',
      endAt: new Date(data.currentBillingPeriod?.endsAt ?? 0),
    }

    await db.subscription.upsert({
      where: {
        subId: String(data.id),
      },
      update: subscriptionPayload,
      create: subscriptionPayload,
    })

    return 'OK'
  }
  catch (error) {
    console.error(error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal Server Error',
    })
  }
})
