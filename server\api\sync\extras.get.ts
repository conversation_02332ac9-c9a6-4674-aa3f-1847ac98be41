export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)

  const query = getQuery(event)
  const updatedAt = query.updatedAt as string
  const limit = parseInt(query.limit as string)

  const extras = await db.extra.findMany({
    where: {
      userId: user.id,
      updatedAt: { gt: new Date(updatedAt) }
    },
    take: limit,
    orderBy: {
      updatedAt: 'asc'
    }
  })

  // 将 extras 中的 updatedAt 转换为毫秒数，似乎无必要，默认保存就是毫秒数
  const extrasWithMillis = extras.map(extra => ({
    ...extra,
    // updatedAt: extra.updatedAt.getTime(),
    // createdAt: extra.createdAt.getTime()
  }));

  const newCheckpoint = extrasWithMillis.length === 0 ? { updatedAt } : {
    updatedAt: extrasWithMillis[extrasWithMillis.length - 1].updatedAt
  };

  return { documents: extrasWithMillis, checkpoint: newCheckpoint }
})
