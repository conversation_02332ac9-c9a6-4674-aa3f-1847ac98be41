<script setup lang="ts">
const route = useRoute()
const links = [
  {
    title: 'Account',
    url: '/dashboard/settings',
  },
  {
    title: 'Billing',
    url: '/dashboard/settings/billing',
  },
]

const isActiveMenuItem = (href: string | null) => {
  return href && route.path === href
}
</script>

<template>
  <div class="mb-8 flex gap-2">
    <Button
      v-for="link in links"
      :key="link.title"
      as-child
      variant="ghost"
      size="sm"
      class="justify-start px-2 text-base"
    >
      <NuxtLink
        class="text-sm"
        :href="link.url"
        :class="isActiveMenuItem(link.url) ? 'bg-secondary font-semibold' : 'text-muted-foreground'"
      >
        {{ link.title }}
      </NuxtLink>
    </Button>
  </div>
</template>
