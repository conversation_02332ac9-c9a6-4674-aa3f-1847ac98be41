<script setup lang="ts">
import { toast } from 'vue-sonner'

const { loggedIn } = useUserSession()

const { baseUrl } = useRuntimeConfig().public
const loading = ref(false)
const props = defineProps({
  variantId: {
    type: String,
    required: true,
    default: () => '',
  },
  isActive: {
    type: Boolean,
    required: true,
  },
})

const createCheckout = async () => {
  if (!loggedIn.value) {
    return navigateTo('/auth/login')
  }

  try {
    loading.value = true
    const data = await $fetch('/api/payment/checkout', {
      method: 'POST',
      body: {
        variantId: props.variantId,
        redirectUrl: `${baseUrl}/dashboard/settings/billing`,
      },
    })
    window.location.href = data
  }
  catch {
    loading.value = false
    toast.error('Error creating checkout link')
  }
}
</script>

<template>
  <Button
    size="lg"
    :disabled="isActive || loading"
    @click="createCheckout"
  >
    <Icon
      v-show="loading"
      name="svg-spinners:180-ring-with-bg"
      class="mr-2"
    /> Get started
  </Button>
</template>
