import { createAuthClient } from "better-auth/vue"
import { inferAdditionalFields } from "better-auth/client/plugins";
import type { auth } from "@@/auth";

const { public: { baseUrl } } = useRuntimeConfig()

export const authClient = createAuthClient({
  /** The base URL of the server (optional if you're using the same domain) */
  baseURL: baseUrl,
  plugins: [inferAdditionalFields<typeof auth>()],
})

// 直接导出 authClient 的方法
export const { signIn, signUp, signOut, useSession, getSession } = authClient;

// 认证状态监听已移至 plugins/auth-listener.client.ts


