import { customAlphabet } from 'nanoid'
import { getClientUploadUrl, getPublicUrl } from '@@/server/libs/storage'

const { storageProvider } = useRuntimeConfig()

export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)
  if (storageProvider === 'local') {
    throw createError({
      statusCode: 500,
      statusMessage:
        'Local storage does not support client uploads. Upload your files directly to Server instead.',
    })
  }
  const { fileName, fileType } = await readBody(event)

  if (!fileName || !fileType) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Missing fileName or fileType',
    })
  }

  const nanoid = customAlphabet('1234567890abcdef', 10)
  const uploadName = `upload/images/${nanoid()}-${fileName}`

  try {
    const url = await getPublicUrl(uploadName)
    const uploadUrl = await getClientUploadUrl(uploadName, fileType)
    return { uploadUrl, uploadName, url }
  }
  catch (error) {
    console.error('Error generating client upload URL:', error)
    throw createError({
      statusCode: 500,
      statusMessage:
        'Failed to generate upload URL. This might not be supported with the current storage provider.',
    })
  }
})
