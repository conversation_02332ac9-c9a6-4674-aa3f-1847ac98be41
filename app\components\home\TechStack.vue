<script setup lang="ts">
const stacks = [
  {
    icon: 'simple-icons:nuxtdotjs',
    size: 'h-8 w-8',
    title: 'Nuxt',
    description: 'The best way to reach humans instead of spam folders. Deliver transactional and marketing emails at scale.',
  },
  {
    icon: 'simple-icons:tailwindcss',
    size: 'h-8 w-8',
    title: 'Tailwindcss',
    description: 'The best way to reach humans instead of spam folders. Deliver transactional and marketing emails at scale.',
  },
  {
    icon: 'lineicons:prisma',
    size: 'h-8 w-8',
    title: 'Prisma',
    description: 'The best way to reach humans instead of spam folders. Deliver transactional and marketing emails at scale.',
  },
  {
    icon: 'devicon-plain:supabase',
    size: 'h-6 w-6',
    title: 'Supabase',
    description: 'The best way to reach humans instead of spam folders. Deliver transactional and marketing emails at scale.',
  },
  {
    icon: 'simple-icons:shadcnui',
    size: 'h-5 w-5',
    title: 'Shadcn/ui',
    description: 'The best way to reach humans instead of spam folders. Deliver transactional and marketing emails at scale.',
  },
  {
    icon: 'devicon-plain:typescript',
    size: 'h-6 w-6',
    title: 'Typescript',
    description: 'The best way to reach humans instead of spam folders. Deliver transactional and marketing emails at scale.',
  },
  {
    icon: 'simple-icons:zod',
    size: 'h-7 w-7',
    title: 'Zod',
    description: 'The best way to reach humans instead of spam folders. Deliver transactional and marketing emails at scale.',
  },
]
</script>

<template>
  <section class="container mt-20 flex items-center justify-center gap-12 font-medium">
    <div class="">
      <h3 class="text-4xl">
        <span class="text-muted-foreground">Build with </span><br>Best tech stack
      </h3>
    </div>
    <div class="flex items-center gap-8">
      <div
        v-for="(stack, index) in stacks"
        :key="index"
      >
        <Tooltip>
          <TooltipTrigger as-child>
            <Icon
              :name="stack.icon"
              :class="stack.size"
              class="text-muted-foreground"
            />
          </TooltipTrigger>
          <TooltipContent class="border bg-card p-4">
            <h5 class="text-lg font-medium text-foreground">
              {{ stack.title }}
            </h5>
            <p class="w-60 text-sm text-muted-foreground">
              {{ stack.description }}
            </p>
          </TooltipContent>
        </Tooltip>
      </div>
    </div>
  </section>
</template>
