<script setup lang="ts">
const Tweets = [
  {
    text: 'Working with @acme has been one of the best dev experiences I\'ve had lately. Incredibly easy to set up, great documentation, and so many fewer hoops to jump through than the competition. I definitely plan to use it on any and all future projects.',
    url: 'https://twitter.com/thatguy_tex/status/1497602628410388480',
    handle: 'thatguy_tex',
    avatarUrl: 'https://i.pravatar.cc/128?u=0',
  },
  {
    text: '@acme is just 🤯 Now I see why a lot of people love using it as a backend for their applications. I am really impressed with how easy it is to set up an Auth and then just code it together for the frontend. @IngoKpp now I see your joy with acme #coding #fullstackwebdev',
    url: 'https://twitter.com/IxoyeDesign/status/1497473731777728512',
    handle: 'IxoyeDesign',
    avatarUrl: 'https://i.pravatar.cc/128?u=1',
  },
  {
    text: 'I\'ve been using @acme for two personal projects and it has been amazing being able to use the power of Postgres and don\'t have to worry about the backend',
    url: 'https://twitter.com/varlenneto/status/1496595780475535366',
    handle: 'varlenneto',
    avatarUrl: 'https://i.pravatar.cc/128?u=2',
  },
  {
    text: 'Y\'all @acme + @nextjs is amazing! 🙌 Barely an hour into a proof-of-concept and already have most of the functionality in place. 🤯🤯🤯',
    url: 'https://twitter.com/justinjunodev/status/1500264302749622273',
    handle: 'justinjunodev',
    avatarUrl: 'https://i.pravatar.cc/128?u=3',
  },
  {
    text: 'And thanks to @acme, I was able to go from idea to launched feature in a matter of hours. Absolutely amazing!',
    url: 'https://twitter.com/BraydonCoyer/status/1511071369731137537',
    handle: 'BraydonCoyer',
    avatarUrl: 'https://i.pravatar.cc/128?u=4',
  },
  {
    text: 'Contributing to open-source projects and seeing merged PRs gives enormous happiness! Special thanks to @acme, for giving this opportunity by staying open-source and being junior-friendly✌🏼',
    url: 'https://twitter.com/damlakoksal/status/1511436907984662539',
    handle: 'damlakoksal',
    avatarUrl: 'https://i.pravatar.cc/128?u=5',
  },
  {
    text: 'Holy crap. @acme is absolutely incredible. Most elegant backend as a service I\'ve ever used. This is a dream.',
    url: 'https://twitter.com/kentherogers/status/1512609587110719488',
    handle: 'KenTheRogers',
    avatarUrl: 'https://i.pravatar.cc/128?u=6',
  },
  {
    text: 'Over the course of a few weeks, we migrated 125.000 users (email/pw, Gmail, Facebook, Apple logins) from Auth0 to @acme and have now completed the migration. I\'m just glad the migration is done 😅 Went well, besides a few edge cases (duplicate emails/linked accounts)',
    url: 'https://twitter.com/kevcodez/status/1518548401587204096',
    handle: 'kevcodez',
    avatarUrl: 'https://i.pravatar.cc/128?u=7',
  },
  {
    text: 'Using @acme I\'m really pleased on the power of postgres (and sql in general). Despite being a bit dubious about the whole backend as a service thing I have to say I really don\'t miss anything. The whole experience feel very robust and secure.',
    url: 'https://twitter.com/paoloricciuti/status/1497691838597066752',
    handle: 'PaoloRicciuti',
    avatarUrl: 'https://i.pravatar.cc/128?u=8',
  },
  {
    text: '@acme is lit. It took me less than 10 minutes to setup, the DX is just amazing.',
    url: 'https://twitter.com/saxxone/status/1500812171063828486',
    handle: 'saxxone',
    avatarUrl: 'https://i.pravatar.cc/128?u=9',
  },
  {
    text: 'I’m not sure what magic @acme is using but we’ve migrated @happyteamsdotio database to @acme from @heroku and it’s much much faster at half the cost.',
    url: 'https://twitter.com/michaelcdever/status/1524753565599690754',
    handle: 'michaelcdever',
    avatarUrl: 'https://i.pravatar.cc/128?u=10',
  },
  {
    text: 'There are a lot of indie hackers building in public, but it’s rare to see a startup shipping as consistently and transparently as acme. Their upcoming March releases look to be 🔥 Def worth a follow! also opened my eyes as to how to value add in open source.',
    url: 'https://twitter.com/swyx/status/1366685025047994373',
    handle: 'swyx',
    avatarUrl: 'https://i.pravatar.cc/128?u=11',
  },
  {
    text: 'This weekend I made a personal record 🥇 on the less time spent creating an application with social login / permissions, database, cdn, infinite scaling, git push to deploy and for free. Thanks to @acme and @vercel',
    url: 'https://twitter.com/jperelli/status/1366195769657720834',
    handle: 'jperelli',
    avatarUrl: 'https://i.pravatar.cc/128?u=12',
  },
  {
    text: 'Badass! acme is amazing. literally saves our small team a whole engineer’s worth of work constantly. The founders and everyone I’ve chatted with at acme are just awesome people as well :)',
    url: 'https://twitter.com/KennethCassel/status/1524359528619384834',
    handle: 'KennethCassel',
    avatarUrl: 'https://i.pravatar.cc/128?u=13',
  },
  {
    text: 'Working with acme is just fun. It makes working with a DB so much easier.',
    url: 'https://twitter.com/the_BrianB/status/1524716498442276864',
    handle: 'the_BrianB',
    avatarUrl: 'https://i.pravatar.cc/128?u=14',
  },
  {
    text: 'This community is STRONG and will continue to be the reason why developers flock to @acme over an alternative. Keep up the good work! ⚡️',
    url: 'https://twitter.com/_wilhelm__/status/1524074865107488769',
    handle: '_wilhelm__',
    avatarUrl: 'https://i.pravatar.cc/128?u=15',
  },
  {
    text: 'Working on my next SaaS app and I want this to be my whole job because I\'m just straight out vibing putting it together. @acme and chill, if you will',
    url: 'https://twitter.com/drewclemcr8/status/1523843155484942340',
    handle: 'drewclemcr8',
    avatarUrl: 'https://i.pravatar.cc/128?u=16',
  },
  {
    text: '@acme Putting a ton of well-explained example API queries in a self-building documentation is just a classy move all around. I also love having GraphQL-style nested queries with traditional SQL filtering. This is pure DX delight. A+++. #backend',
    url: 'https://twitter.com/CodiferousCoder/status/1522233113207836675',
    handle: 'CodiferousCoder',
    avatarUrl: 'https://i.pravatar.cc/128?u=17',
  },
  {
    text: 'Me using @acme for the first time right now 🤯',
    url: 'https://twitter.com/nasiscoe/status/1365140856035024902',
    handle: 'nasiscoe',
    avatarUrl: 'https://i.pravatar.cc/128?u=18',
  },
  {
    text: 'I\'m trying @acme, Firebase alternative that uses PostgreSQL (and you can use GraphQL too) in the cloud. It\'s incredible 😍',
    url: 'https://twitter.com/JP__Gallegos/status/1365699468109242374',
    handle: 'JP__Gallegos',
    avatarUrl: 'https://i.pravatar.cc/128?u=19',
  },
  {
    text: 'Check out this amazing product @acme. A must give try #newidea #opportunity',
    url: 'https://twitter.com/digitaldaswani/status/1364447219642814464',
    handle: 'digitaldaswani',
    avatarUrl: 'https://i.pravatar.cc/128?u=20',
  },
  {
    text: 'I gave @acme a try this weekend and I was able to create a quick dashboard to visualize the data from the PostgreSQL instance. It\'s super easy to use acme\'s API or the direct DB connection. Check out the tutorial 📖',
    url: 'https://twitter.com/razvanilin/status/1363770020581412867',
    handle: 'razvanilin',
    avatarUrl: 'https://i.pravatar.cc/128?u=21',
  },
  {
    text: 'Tried @acme for the first time yesterday. Amazing tool! I was able to get my Posgres DB up in no time and their documentation on operating on the DB is super easy! 👏 Can\'t wait for Cloud functions to arrive! It\'s gonna be a great Firebase alternative!',
    url: 'https://twitter.com/chinchang457/status/1363347740793524227',
    handle: 'chinchang457',
    avatarUrl: 'https://i.pravatar.cc/128?u=22',
  },
  {
    text: 'I gave @acme a try today and I was positively impressed! Very quick setup to get a working remote database with API access and documentation generated automatically for you 👌 10/10 will play more',
    url: 'https://twitter.com/razvanilin/status/1363002398738800640',
    handle: 'razvanilin',
    avatarUrl: 'https://i.pravatar.cc/128?u=23',
  },
  {
    text: 'Wait. Is it so easy to write queries for @acme ? It\'s like simple SQL stuff!',
    url: 'https://twitter.com/T0ny_Boy/status/1362911838908911617',
    handle: 'T0ny_Boy',
    avatarUrl: 'https://i.pravatar.cc/128?u=24',
  },
  {
    text: 'Jeez, and @acme have native support for magic link login?! I was going to use http://magic.link for this But if I can get my whole DB + auth + magic link support in one... Awesome',
    url: 'https://twitter.com/louisbarclay/status/1362016666868154371',
    handle: 'louisbarclay',
    avatarUrl: 'https://i.pravatar.cc/128?u=25',
  },
  {
    text: '@MongoDB or @MySQL?!?! Please, let me introduce you to @acme and the wonderful world of @PostgreSQL before it\'s too late!!',
    url: 'https://twitter.com/jim_bisenius/status/1361772978841788416',
    handle: 'jim_bisenius',
    avatarUrl: 'https://i.pravatar.cc/128?u=26',
  },
  {
    text: 'Where has @acme been all my life? 😍',
    url: 'https://twitter.com/Elsolo244/status/1360257201911320579',
    handle: 'Elsolo244',
    avatarUrl: 'https://i.pravatar.cc/128?u=27',
  },
  {
    text: 'Honestly acme is such a killer Firebase alternative.',
    url: 'https://twitter.com/XPCheese/status/1360229397735895043',
    handle: 'XPCheese',
    avatarUrl: 'https://i.pravatar.cc/128?u=28',
  },
  {
    text: 'I think you\'ll love @acme :-) Open-source, PostgreSQL-based & zero magic.',
    url: 'https://twitter.com/zippoxer/status/1360021315852328961',
    handle: 'zippoxer',
    avatarUrl: 'https://i.pravatar.cc/128?u=29',
  },
]

const tweets = ref(Tweets.slice(0, 10))
const showButton = ref(true)

function handleShowMore() {
  tweets.value = [
    ...tweets.value,
    ...Tweets.slice(tweets.value.length, tweets.value.length + 10),
  ]

  if (tweets.value.length >= Tweets.length)
    showButton.value = false
}
</script>

<template>
  <div class="relative columns-1 gap-4 overflow-hidden transition-all sm:columns-2 lg:columns-3 xl:columns-4">
    <div
      v-show="showButton"
      class="absolute bottom-0 left-0 z-10 h-[240px] w-full bg-gradient-to-t from-background via-background"
    />
    <div
      v-for="(tweet, index) in tweets"
      :key="index"
      class="z-0 mb-4 break-inside-avoid-column"
    >
      <div class="rounded-lg border bg-muted p-6 drop-shadow-sm dark:bg-transparent">
        <div class="relative">
          <div class="flex items-center gap-2">
            <div class="size-10 overflow-hidden rounded-full border">
              <Image
                :src="tweet.avatarUrl"
                class="size-9"
              />
            </div>
            <p class="text-sm font-medium text-foreground">
              @{{ tweet.handle }}
            </p>
            <div class="absolute -left-1 -top-1 flex size-5 items-center justify-center rounded-full bg-black">
              <svg
                class="size-3"
                fill="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
              </svg>
            </div>
          </div>
        </div>
        <p class="mt-3 text-base text-muted-foreground">
          "{{ tweet.text }}"
        </p>
      </div>
    </div>
    <div class="absolute inset-x-0 bottom-0 z-20 mb-10 flex justify-center">
      <Button
        v-show="showButton"
        variant="secondary"
        type="default"
        @click="handleShowMore"
      >
        Show More
      </Button>
    </div>
  </div>
</template>
