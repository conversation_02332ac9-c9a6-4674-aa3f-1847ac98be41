/**
 * 扩展端通信工具
 * 用于网站与浏览器扩展之间的消息传递
 */

/**
 * 向扩展端发送消息
 * @param action 要执行的动作
 * @param data 可选的附加数据
 */
export function notifyExtension(action: string, data?: any) {
  try {
    const message = {
      type: 'website_to_ext',
      action,
      ...(data && { data })
    };
    
    window.postMessage(message, '*');
  } catch (error) {
    console.warn('Failed to notify extension:', error);
  }
}
