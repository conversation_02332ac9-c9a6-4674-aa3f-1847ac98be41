---
title: License
description: Get started with Nuxt UI Pro documentation template.
date: 2024-08-25
---

Read about the licensing terms for Acme. Have a question? Feel free to [contact us](mailto:<EMAIL>).

Acme grants you an ongoing, non-exclusive license to **use** the download files. The license grants permission to one individual (the Licensee) to access and use the download files.

## Permissible Actions:

- Use the download files to create unlimited End Products.
- Use the download files to create unlimited End Products for unlimited Clients.
- Use the download files to create End Products where the End Product is sold to End Users.
- Use the download files to create End Products that are open source and freely available to End Users following review and approval by Acme.
- Sell the End Products. The License will survive the sale of the End Product.

## Prohibited Actions:

- Use the download files to create End Products that are designed to allow an End User to build their own End Products using the download files or derivatives of the download files.
- Re-distribute the download files or derivatives of the download files separately from an End Product, neither in code or as design assets.
- Share your access to the download files with any other individuals

## Permissible Examples

Examples of usage allowed by the license:

- Creating a personal website by yourself
- Creating a website or web application for a client that will be owned by that client
- Creating a commercial SaaS application (like an invoicing app for example) where end users have to pay a fee to use the application
- Creating a commercial self-hosted web application that is sold to end users for a one-time fee
- Creating a web application where the primary purpose is clearly not to simply re-distribute the download files (like a conference organization app that uses the download files for its UI for example) that is free and open source, where the source code is publicly available

## Examples of usage not allowed by the license:

- Creating a theme, template, or project starter kit using the download files and making it available either for sale or for free

In simple terms, use Acme for anything you like except for projects that compete with Acme and would have a negative impact on our product sales.

## Developer License Definitions

- Licensee is the individual who has purchased a Standard License.
- Download files are the files that are downloaded from the Acme repository.
- End Product is any artifact produced that incorporates the download files or derivatives of the download files.
- End User is a user of an End Product.
- Client is an individual or entity receiving custom professional services directly from the Licensee, produced specifically for that individual or entity. Customers of software-as-a-service products are not considered clients for the purpose of this document.

## Team License

Acme grants you an ongoing, non-exclusive license to use the download files. The license grants permission to one individual (the Licensee) to access and use the download files.

The license grants permission to all Employees and Contractors of the Licensee to access and use the download files.

## Permissible Actions:

- Use the download files to create unlimited End Products.
- Use the download files to create unlimited End Products for unlimited Clients.
- Use the download files to create End Products where the End Product is sold to End Users.
- Use the download files to create End Products that are open source and freely available to End Users following review and approval by Acme.
- Sell the End Products. The License will survive the sale of the End Product.

## Prohibited Actions:

- Use the download files to create End Products that are designed to allow an End User to build their own End Products using the download files or derivatives of the download files.
- Re-distribute the download files or derivatives of the download files separately from an End Product.

## Example usage

### Examples of usage allowed by the license:

- Creating a website for your company
- Creating a website or web application for a client that will be owned by that client
- Creating a commercial SaaS application (like an invoicing app for example) where end users have to pay a fee to use the application
- Creating a commercial self-hosted web application that is sold to end users for a one-time fee
- Creating a web application where the primary purpose is clearly not to simply re-distribute the download files (like a conference organization app that uses the download files for its UI for example) that is free and open source, where the source code is publicly available

### Examples of use not allowed by the license:

- Creating a theme or template using the download files and making it available either for sale for for free
- Creating any End Product that is not the sole property of either your company or a client of your company. For example your employees/contractors can not use your company Acme license to build their own websites or side projects.

## Team License Definitions

- Licensee is the business entity who has purchased a Team License.
- Download files are the files that are downloaded from the Acme repository.
- End Product is any artifact produced that incorporates the download files.
- End User is a user of an End Product.
- Employee is a full-time or part-time employee of the Licensee.
- Contractor is an individual or business entity contracted to perform services for the Licensee.
- Client is an individual or entity receiving custom professional services directly from the Licensee, produced specifically for that individual or entity. Customers of software-as-a-service products are not considered clients for the purpose of this document.

## Liability

Acme's liability to you for costs, damages, or other losses arising from your use of the download files — including third-party claims against you — is limited to a refund of your license fee. Acme may not be held liable for any consequential damages related to your use of the download files.

## Refunds

Due to the non-returnable nature of this product, we do not offer refunds.