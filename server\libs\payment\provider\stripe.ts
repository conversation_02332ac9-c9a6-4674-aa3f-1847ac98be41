import Stripe from 'stripe'
import type {
  Product,
  ProductVariant,
  CheckoutOptions,
  CustomerPortalOptions,
  SubscriptionStatus,
  Transaction,
} from '../types'

let stripeClient: Stripe | null = null

function getStripeClient() {
  if (stripeClient) {
    return stripeClient
  }

  const stripeSecretKey = process.env.STRIPE_SECRET_KEY!

  if (!stripeSecretKey) {
    throw new Error('Missing env variable STRIPE_SECRET_KEY')
  }

  stripeClient = new Stripe(stripeSecretKey)

  return stripeClient
}

export const getProducts = async (): Promise<Product[]> => {
  const stripeClient = getStripeClient()

  const response = await stripeClient.prices.list({
    active: true,
    expand: ['data.product'],
  })

  const plansMap: Record<string, Product> = {}

  response.data.forEach((price) => {
    const product = price.product as Stripe.Product

    if (!plansMap[product.id]) {
      plansMap[product.id] = {
        id: product.id,
        name: product.name,
        description: product.description,
        storeId: null,
        variants: [],
      }
    }

    const variant: ProductVariant = {
      id: price.id,
      interval: price.recurring?.interval ?? 'year',
      interval_count: price.recurring?.interval_count ?? 0,
      price: price.unit_amount ?? 0,
      currency: price.currency,
    }

    plansMap[product.id].variants.push(variant)
  })

  return Object.values(plansMap).filter(plan => plan.variants.length > 0)
}

export const createCheckoutLink = async (options: CheckoutOptions): Promise<string> => {
  const stripeClient = getStripeClient()

  const session = await stripeClient.checkout.sessions.create({
    mode: 'subscription',
    success_url: options.redirectUrl ?? '',
    line_items: [
      {
        quantity: 1,
        price: options.variantId,
      },
    ],
    customer_email: options.email,
    subscription_data: {
      metadata: {
        user_id: options.userId,
      },
    },
  })

  if (!session.url) {
    throw new Error('Failed to create checkout session')
  }

  return session.url
}

export const createCustomerPortalLink = async (options: CustomerPortalOptions): Promise<string> => {
  const stripeClient = getStripeClient()

  const sessionParams: any = {
    customer: options.customerId,
    return_url: options.redirectUrl ?? '',
  }

  // Add flow_data if provided
  if (options.flowData) {
    sessionParams.flow_data = options.flowData
  }

  const session = await stripeClient.billingPortal.sessions.create(sessionParams)

  return session.url
}

export const createCustomer = async (email: string, name: string): Promise<string> => {
  const stripeClient = getStripeClient()

  const customer = await stripeClient.customers.create({
    email,
    name,
  })
  return customer.id
}

export const pauseSubscription = async (id: string): Promise<void> => {
  const stripeClient = getStripeClient()

  await stripeClient.subscriptions.update(id, {
    pause_collection: {
      behavior: 'void',
    },
  })
}

export const cancelSubscription = async (id: string): Promise<void> => {
  const stripeClient = getStripeClient()

  await stripeClient.subscriptions.cancel(id)
}

export const resumeSubscription = async (id: string): Promise<{ status: SubscriptionStatus }> => {
  const stripeClient = getStripeClient()

  const subscription = await stripeClient.subscriptions.resume(id, {
    billing_cycle_anchor: 'unchanged',
  })

  return {
    status: subscription.status as SubscriptionStatus,
  }
}

export const updateSubscription = async (customerId: string, newPriceId: string): Promise<void> => {
  const stripeClient = getStripeClient()

  const subscriptions = await stripeClient.subscriptions.list({
    customer: customerId,
  })
  if (!subscriptions.data.length) {
    throw new Error('No subscriptions found for the customer')
  }
  const subscription = subscriptions.data[0]
  const subscriptionItemId = subscription.items.data[0].id
  await stripeClient.subscriptionItems.update(subscriptionItemId, {
    price: newPriceId,
  })
}

async function processStripeTransactions(
  stripeResponse: Stripe.Response<Stripe.ApiList<Stripe.PaymentIntent>>,
): Promise<Transaction[]> {
  const stripeClient = getStripeClient()

  const processedTransactions: Transaction[] = []

  for (const paymentIntent of stripeResponse.data) {
    let customerInfo = paymentIntent.customer as string

    if (paymentIntent.customer) {
      try {
        const customer = await stripeClient.customers.retrieve(
          paymentIntent.customer as string,
        )
        customerInfo = (customer as Stripe.Customer).email || (paymentIntent.customer as string)
      }
      catch (error) {
        console.error(`Error fetching customer ${paymentIntent.customer}:`, error)
      }
    }

    processedTransactions.push({
      id: paymentIntent.id,
      amount: (paymentIntent.amount / 100).toFixed(2),
      currency: paymentIntent.currency,
      status: paymentIntent.status,
      date: new Date(paymentIntent.created * 1000).toISOString(),
      customer: customerInfo,
      description: paymentIntent.description || '',
      invoice: paymentIntent.invoice as string | null,
    })
  }

  return processedTransactions
}

export const listAllTransactions = async (): Promise<Transaction[]> => {
  const stripeClient = getStripeClient()

  const paymentIntents = await stripeClient.paymentIntents.list()
  return processStripeTransactions(paymentIntents)
}

export const getInvoice = async (invoiceId: string): Promise<any> => {
  const stripeClient = getStripeClient()

  try {
    const invoice = await stripeClient.invoices.retrieve(invoiceId)
    return invoice
  }
  catch (error) {
    console.error('Error retrieving invoice:', error)
    throw new Error('Failed to retrieve invoice')
  }
}
