<script setup lang="ts">
import { TreeItem, TreeRoot } from 'reka-ui'

const items = [
  { title: '.vercel', icon: 'lucide:folder', description: 'Deploy to vercel.' },
  {
    title: 'app',
    description: '',
    children: [
      { title: 'assets',
        icon: 'lucide:folder',
        description: 'All the website\'s assets that the build tool will process.',
        children: [
          {
            title: 'css',
            icon: 'lucide:folder',
            description: '',
            children: [
              { title: 'tailwind.css', description: '' },
              { title: 'themes.css', description: '' },
            ],
          }],
      },
      {
        title: 'components',
        icon: 'lucide:folder',
        description: 'Nuxt automatically imports any components in this directory.',
        children: [
          {
            title: 'admin',
            icon: 'lucide:folder',
            description: 'Super Admin components',
          },
          {
            title: 'content',
            icon: 'lucide:folder',
            description: 'File-based CMS components',
          },
          {
            title: 'billing',
            icon: 'lucide:folder',
            description: 'Billing components',
          },
          {
            title: 'dashboard',
            icon: 'lucide:folder',
            description: 'Dashboard components',
          },
          {
            title: 'ui',
            icon: 'lucide:folder',
            description: 'Shadcn/ui components',
          },
        ],
      },
      {
        title: 'composables',
        icon: 'lucide:folder',
        description: 'Composables will get auto imported when used.',
      },
      { title: 'layouts', icon: 'lucide:folder', description: '' },
      { title: 'middleware', icon: 'lucide:folder', description: '', children: [
        { title: 'admin.ts', description: '' },
        { title: 'auth.ts', description: '' },
      ] },
      { title: 'pages', icon: 'lucide:folder', description: '', children: [
        {
          title: 'admin',
          icon: 'lucide:folder',
          description: '',
        },
        {
          title: 'auth',
          icon: 'lucide:folder',
          description: '',
        },
        {
          title: 'dashboard',
          icon: 'lucide:folder',
          description: '',
        },
        {
          title: 'admin.vue',
          description: '',
        },
        {
          title: 'dashboard.vue',
          description: '',
        },
        {
          title: 'index.vue',
          description: '',
        },
      ] },
      { title: 'store', icon: 'lucide:folder', description: 'Pinia' },
      { title: 'utils', icon: 'lucide:folder', description: '' },
      { title: 'app.config.ts', description: '' },
      { title: 'app.vue', description: '' },
      { title: 'error.vue', description: '' },
    ],
  },
  { title: 'content', icon: 'lucide:folder', description: 'File-based CMS for your application.', children: [
    {
      title: 'blogs',
      icon: 'lucide:folder',
      description: '',
    },
    {
      title: 'docs',
      icon: 'lucide:folder',
      description: '',
    },
    {
      title: 'legal',
      icon: 'lucide:folder',
      description: '',
    },
  ],
  },
  { title: 'modules', icon: 'lucide:folder', description: '' },
  { title: 'prisma', icon: 'lucide:folder', description: '', children: [
    {
      title: 'migrations',
      icon: 'lucide:folder',
      description: '',
    },
    {
      title: 'zod',
      icon: 'lucide:folder',
      description: '',
    },
    {
      title: 'schema.prisma',
      description: '',
    },
  ] },
  { title: 'public', icon: 'lucide:folder', description: '' },
  { title: 'server', icon: 'lucide:folder', description: '', children: [
    {
      title: 'api',
      icon: 'lucide:folder',
      description: '',
      children: [
        { title: 'account', icon: 'lucide:folder', description: '' },
        { title: 'admin', icon: 'lucide:folder', description: '' },
        { title: 'auth', icon: 'lucide:folder', description: '' },
        { title: 'payment', icon: 'lucide:folder', description: '' },
      ],
    },
    {
      title: 'libs',
      icon: 'lucide:folder',
      description: '',
      children: [
        { title: 'email', icon: 'lucide:folder', description: '' },
        { title: 'payment', icon: 'lucide:folder', description: '' },
        { title: 'storage', icon: 'lucide:folder', description: '' },
      ],
    },
    {
      title: 'middleware',
      icon: 'lucide:folder',
      description: '',
      children: [
        { title: 'auth.ts', description: '' },
      ],
    },
    {
      title: 'utils',
      icon: 'lucide:folder',
      description: '',
    },
    {
      title: 'tsconfig.json',
      description: '',
    },
  ] },
  { title: 'shared', icon: 'lucide:folder', description: '' },
  { title: '.env', icon: 'vscode-icons:file-type-vue', description: '' },
  { title: '.env.example', icon: 'vscode-icons:file-type-vue', description: '' },
  { title: 'eslint.config.mjs', icon: 'vscode-icons:file-type-vue', description: '' },
  { title: 'nuxt.config.ts', icon: 'vscode-icons:file-type-nuxt', description: '' },
  { title: 'package.json', icon: 'vscode-icons:file-type-nuxt', description: '' },
  { title: 'tailwind.config.ts', icon: 'vscode-icons:file-type-vue', description: '' },
  { title: 'tsconfig.json', icon: 'vscode-icons:file-type-vue', description: '' },
]
</script>

<template>
  <TreeRoot
    v-slot="{ flattenItems }"
    class="list-none rounded-lg bg-muted p-2 text-muted-foreground"
    style="margin-left: 0px;"
    :items="items"
    :get-key="(item) => item.title"
    :default-expanded="['app']"
  >
    <TreeItem
      v-for="item in flattenItems"
      v-slot="{ isExpanded }"
      :key="item._id"
      :style="{ 'padding-left': `${item.level - 0.5}rem` }"
      v-bind="item.bind"
      class="flex items-center rounded px-2 py-0 outline-none"
    >
      <template v-if="item.hasChildren">
        <Icon
          v-if="!isExpanded"
          name="lucide:folder-plus"
        />
        <Icon
          v-else
          name="lucide:folder"
          class=""
        />
      </template>
      <Icon
        v-else
        :name="item.value.icon || 'lucide:file'"
        class=""
      />
      <div class="pl-2">
        <span class="text-sm text-foreground">{{ item.value.title }}</span>
        <span
          v-if="item.value.description"
          class="ml-2 text-sm text-muted-foreground/60"
        > # {{ item.value.description }}</span>
      </div>
    </TreeItem>
  </TreeRoot>
</template>
