import { customAlphabet } from 'nanoid'
import { validateBlob } from '@@/server/utils/blob'
import { upload } from '@@/server/libs/storage'

export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)
  const form = await readFormData(event)
  const file = form.get('image')

  if (!(file instanceof Blob)) {
    throw new Error('Image is not a Blob')
  }
  validateBlob(file, { maxSize: '1MB', types: ['image/png', 'image/jpeg', 'image/webp'] })
  const nanoid = customAlphabet('1234567890abcdef', 10)
  const filename = `${nanoid()}-${file.name}`

  const publicUrl = await upload({ name: `upload/images/${filename}`, data: file })

  const image = await db.image.create({
    data: {
      userId: user.id,
      name: filename,
      url: publicUrl,
    },
  })

  return image
})
