<script setup lang="ts">
import type { Table } from '@tanstack/vue-table'

defineProps<{
  table: Table<any>
  page: number
  pageSize: number
  total: number
}>()

const emit = defineEmits<{
  updatePage: [value: number]
  updatePageSize: [value: number]
}>()
</script>

<template>
  <div class="flex items-center justify-between px-2">
    <div class="flex-1 text-sm text-muted-foreground">
      {{ table.getFilteredSelectedRowModel().rows.length }} of
      {{ table.getFilteredRowModel().rows.length }} row(s) selected.
    </div>
    <div class="flex items-center space-x-6 lg:space-x-8">
      <div class="flex items-center space-x-2">
        <p class="text-sm font-medium text-muted-foreground">
          Rows per page
        </p>
        <Select
          :model-value="pageSize"
          @update:model-value="(value) => emit('updatePageSize', +value)"
        >
          <SelectTrigger class="h-7 w-16">
            <SelectValue :placeholder="`${pageSize}`" />
          </SelectTrigger>
          <SelectContent side="top">
            <SelectItem
              v-for="size in [1, 10, 20, 50]"
              :key="size"
              :value="size"
            >
              {{ size }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Pagination
        v-slot="{ page: currentPage }"
        :page="page"
        :items-per-page="pageSize"
        :total="total"
        :sibling-count="1"
        show-edges
        :default-page="1"
        @update:page="(value: number) => emit('updatePage', value)"
      >
        <PaginationList
          v-slot="{ items }"
          class="flex items-center justify-center gap-1"
        >
          <PaginationFirst class="size-7" />
          <PaginationPrevious class="size-7" />
          <template v-for="(item, index) in items">
            <PaginationListItem
              v-if="item.type === 'page'"
              :key="index"
              :value="item.value"
              as-child
            >
              <Button
                class="size-7 p-0 font-normal"
                :variant="item.value === currentPage ? 'secondary' : 'outline'"
              >
                {{ item.value }}
              </Button>
            </PaginationListItem>
            <PaginationEllipsis
              v-else
              :key="item.type"
              :index="index"
            />
          </template>
          <PaginationNext class="size-7" />
          <PaginationLast class="size-7" />
        </PaginationList>
      </Pagination>
    </div>
  </div>
</template>
