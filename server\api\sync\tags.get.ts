export default defineEventHandler(async (event) => {
  // const { user } = await requireUserSession(event)

  const query = getQuery(event)
  console.warn('-------- query ---------', query)
  const id = query.id || ''
  const updatedAt = query.updatedAt as string
  const limit = parseInt(query.limit as string)

  const tags = await db.tag.findMany({
    where: {
      // userId: user.id,
      updatedAt: { gt: new Date(updatedAt) }
    },
    take: limit,
    orderBy: {
      updatedAt: 'asc'
    }
  })

  // 将 tags 中的 updatedAt 转换为毫秒数
  const tagsWithMillis = tags.map(tag => ({
    ...tag,
    // updatedAt: tag.updatedAt.getTime(),
    // createdAt: tag.createdAt.getTime()
  }));

  const newCheckpoint = tagsWithMillis.length === 0 ? { updatedAt } : {
    updatedAt: tagsWithMillis[tagsWithMillis.length - 1].updatedAt
  };

  return { documents: tagsWithMillis, checkpoint: newCheckpoint }
})
