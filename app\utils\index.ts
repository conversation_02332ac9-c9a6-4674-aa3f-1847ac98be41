import type { Updater } from '@tanstack/vue-table'

export function valueUpdater<T>(updaterOrValue: Updater<T> | T, ref: Ref<T>) {
  ref.value
    = typeof updaterOrValue === 'function'
      ? (updaterOrValue as (old: T) => T)(ref.value)
      : updaterOrValue
}

export function avatarName(name: string) {
  if (!name) {
    return 'N/A'
  }
  return name
    .split(' ')
    .map((chunk: string) => chunk[0])
    .join('')
}

export function formatMoney(amount: number = 0, currency: string = 'USD') {
  const language = typeof navigator !== 'undefined' ? navigator.language : 'en-US'
  return new Intl.NumberFormat(language ?? 'en-US', {
    style: 'currency',
    currency: currency,
  }).format(amount)
}

// 通用函数，用于提取 url 中的 domain
export function getDomain(url: string) {
  const parsedUrl = new URL(url)
  return parsedUrl.hostname.replace(/^www\./, '')
}

// 预设常用 emoji 列表
export const commonEmojis = ['🚩','🚀', '⚡', '😀', '🔥', '🏆', '🎉', '⭐', '❤️', '💡', '😈', '💩'] 

export function faviconURL(u: string) {
  try {
    const domain = getDomain(u);
    // 使用 Google 的 favicon 服务
    return `https://www.google.com/s2/favicons?domain=${domain}&sz=32`;
  } catch (error) {
    console.error('获取 favicon 失败:', error);
    // 返回一个默认图标
    return '/default-favicon.png';
  }
}

/**
 * 计算字符串的哈希值 (简单哈希，快速但碰撞风险较高)
 * @param str 需要计算哈希的字符串
 * @returns 哈希字符串 (8字符)
 */
export function hashString(str: string): string {
  // 使用简单的哈希算法计算哈希值
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }

  // 转换为16进制字符串，确保结果为正数
  return (hash >>> 0).toString(16);
}
