<script setup lang="ts">
import type { Environments, Paddle } from '@paddle/paddle-js'
import { initializePaddle } from '@paddle/paddle-js'

const paddle = ref<Paddle | undefined>(undefined)

const { user } = useUserSession()

const loading = ref(false)
const props = defineProps({
  variantId: {
    type: String,
    required: true,
    default: () => '',
  },
  isActive: {
    type: Boolean,
    required: true,
  },
})

const paddleClientToken = import.meta.env.VITE_PADDLE_CLIENT_TOKEN
const paddleEnv = import.meta.env.VITE_PADDLE_ENV
const openCheckout = async () => {
  const paddleInstance = await initializePaddle({
    token: paddleClientToken,
    environment: paddleEnv as Environments,
    checkout: {
      settings: {
        displayMode: 'overlay',
        // theme: 'dark',
        variant: 'one-page',
        // allowLogout: !userEmail,
        // frameTarget: 'paddle-checkout-frame',
        frameInitialHeight: 450,
        frameStyle: 'width: 100%; background-color: transparent; border: none',
        successUrl: '/dashboard/settings/billing',
      },
    },
  })

  if (paddleInstance) {
    paddle.value = paddleInstance
    paddleInstance.Checkout.open({
      customData: {
        user_id: user.value?.id ?? '',
      },
      items: [{ priceId: props.variantId, quantity: 1 }],
    })
  }
}
</script>

<template>
  <Button
    size="lg"
    :disabled="isActive || loading"
    @click="openCheckout"
  >
    <Icon
      v-show="loading"
      name="svg-spinners:180-ring-with-bg"
      class="mr-2"
    /> Get started
  </Button>
</template>
