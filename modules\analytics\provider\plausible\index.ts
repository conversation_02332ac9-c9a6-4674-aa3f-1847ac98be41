// If you need to enable this feature, please uncomment this code and install the required package.

/*
import { useHead } from '#imports'

export const useAnalytics = () => {
  const init = () => {
    if (!import.meta.client || !import.meta.env.VITE_PLAUSIBLE_URL) {
      return
    }

    const plausibleUrl = import.meta.env.VITE_PLAUSIBLE_URL

    useHead({
      script: [
        {
          'defer': true,
          'key': 'analytics-plausible',
          'src': 'https://plausible.io/js/script.js',
          'data-domain': plausibleUrl,
        },
      ],
    })
  }

  const trackEvent = (event: string, data?: Record<string, any>) => {
    if (typeof window === 'undefined' || !(window as any).plausible) {
      return
    }

    (window as any).plausible(event, {
      props: data,
    })
  }

  return {
    init,
    trackEvent,
  }
}
*/
