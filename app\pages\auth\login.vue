<script setup lang="ts">
import { authClient } from "~/utils/auth-client"; 
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { z } from 'zod'

definePageMeta({
  title: 'Login',
  layout: false,
})

const loading = ref(false)

const formSchema = toTypedSchema(
  z.object({
    email: z.string().email(),
  }),
)

const form = useForm({
  validationSchema: formSchema,
})

const onSubmit = form.handleSubmit(async (values) => {
  try {
    await $fetch('/api/auth/send-opt', { method: 'POST', body: values })
    toast(`We just sent a code to ${values.email}`)
    await navigateTo(`/auth/verify-opt?email=${values.email}`)
  } catch (error) {
    toast.error(error.data?.statusMessage || error.data?.message || 'An unknown error occurred')
  } finally {
    loading.value = false
  }
})

const signinWithGoogle = async () => {
  console.warn('~~~~~~~signinWithGoogle~~~~~~~')
  await authClient.signIn.social({
      provider: "google",
      callbackURL: "/dashboard", 
      errorCallbackURL: "/error",
      newUserCallbackURL: "/dashboard",
      disableRedirect: false,
  });
}
</script>

<template>
  <Button
    as-child
    variant="ghost"
    class="absolute left-0 top-6 rounded-full text-sm font-semibold text-muted-foreground md:left-6"
  >
    <NuxtLink href="/">
      <Icon
        name="radix-icons:caret-left"
        class="mr-1"
      /> Home
    </NuxtLink>
  </Button>
  <div class="flex h-screen items-center justify-center">
    <main class="mx-auto min-h-[490px] w-full max-w-[450px]">
      <h1 class="mb-1.5 mt-8 text-center text-xl font-bold tracking-[-0.16px] sm:text-left">
        Login to Gomark
      </h1>
      <p class="text-center text-base font-normal text-muted-foreground sm:text-left">
        If you don't have an account, we'll create one
      </p>
      
      <div class="mt-10 flex flex-col items-center gap-4">
        <Button
          class="w-full"
          size="lg"
          variant="outline"
          type="button"
          @click="signinWithGoogle"
        >
          <!-- <Icon name="custom:ri-google-fill" class="mr-2 size-5" /> -->
          Login with Google
        </Button>
      </div>

      <div class="mb-6 mt-6 flex items-center justify-center">
        <span class="text-xs text-muted-foreground mx-4">OR</span>
      </div>
      
      <form
        class="flex flex-col items-stretch gap-4"
        @submit.prevent="onSubmit"
      >
        <FormField
          v-slot="{ componentField }"
          name="email"
        >
          <FormItem>
            <FormControl>
              <Input
                v-bind="componentField"
                autocomplete="email"
                placeholder="Enter your email"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
        <Button
          class="w-full disabled:opacity-60 font-semibold"
          type="submit"
          size="lg"
        >
          <Icon
            v-if="loading"
            name="svg-spinners:3-dots-fade"
            class="mr-2 size-4"
          />
          <span v-else class="flex items-center gap-2">Continue 
          </span>
        </Button>
      </form>
      <p class="mt-8 text-xs text-muted-foreground">
        Or you can 
        <NuxtLink
          class="text-primary hover:underline"
          href="/auth/login-with-password"
        >
          Login with password
        </NuxtLink>
      </p>
    </main>
  </div>
</template>
